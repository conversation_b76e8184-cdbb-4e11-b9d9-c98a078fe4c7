// frontend\src\pages\embed\[id].tsx

import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Head from "next/head";
import axios from "axios";
import dynamic from "next/dynamic";
import { Panorama, Hotspot } from "@/types/hotspot";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import { PanoramaLanguageToggle } from "@/components/PanoramaLanguageToggle";
import { usePanoramaLanguageStore } from "@/store/panoramaLanguageStore";

// Dynamically import View360PanoramaViewer to avoid SSR issues
const View360PanoramaViewer = dynamic(
  () => import("@/components/View360PanoramaViewer"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-full bg-muted">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading 360° viewer...</p>
        </div>
      </div>
    ),
  }
);

const EmbedPanoramaPage: React.FC = () => {
  const { t } = useAppTranslation();
  const router = useRouter();
  const { id } = router.query;
  const [panorama, setPanorama] = useState<Panorama | null>(null);
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Panorama content language state
  const { contentLanguage, setContentLanguage } = usePanoramaLanguageStore();

  useEffect(() => {
    if (id) {
      fetchPanoramaData();
    }
  }, [id, contentLanguage]);

  const fetchPanoramaData = async () => {
    try {
      setLoading(true);

      // Fetch panorama details
      const panoramaResponse = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${id}`
      );

      const panoramaData = panoramaResponse.data.data.panorama;

      // For embed, we might want to show inactive panoramas too (for preview)
      setPanorama(panoramaData);

      // Fetch hotspots for this panorama with selected language
      const hotspotsResponse = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/panorama/${id}?lang=${contentLanguage}`
      );

      setHotspots(hotspotsResponse.data.data.hotspots || []);
    } catch (error: any) {
      console.error("Error fetching panorama:", error);
      if (error.response?.status === 404) {
        setError(t("embedPage.errors.notFound", "Panorama not found"));
      } else {
        setError(t("embedPage.errors.loadFailed", "Failed to load panorama"));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    console.log("Hotspot clicked in embed:", hotspot);
  };

  if (loading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">
            {t("embedPage.loading", "Loading panorama...")}
          </p>
        </div>
      </div>
    );
  }

  if (error || !panorama) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <h1 className="text-xl font-bold text-destructive">
            {error || t("embedPage.errors.notFound", "Panorama not found")}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t(
              "embedPage.errors.notAvailable",
              "The panorama you're looking for doesn't exist or is not available."
            )}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>
          {panorama.title} - {t("embedPage.meta.titleSuffix", "Embedded View")}
        </title>
        <meta
          name="description"
          content={t(
            "embedPage.meta.description",
            "Embedded 360° view of {title}",
            { title: panorama.title }
          )}
        />
        <meta name="robots" content="noindex, nofollow" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
        />
        <style jsx global>{`
          html,
          body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
          }
          #__next {
            height: 100%;
          }
        `}</style>
      </Head>

      <div className="h-screen w-full relative">
        {/* Panorama Language Toggle - Upper Right Corner */}
        <div className="absolute top-4 right-4 z-20">
          <PanoramaLanguageToggle
            selectedLanguage={contentLanguage}
            onLanguageChange={setContentLanguage}
            variant="outline"
            size="sm"
            showLabel={false}
          />
        </div>

        <View360PanoramaViewer
          panoramaUrl={`${process.env.NEXT_PUBLIC_API_URL}${panorama.imageUrl}`}
          hotspots={hotspots}
          onHotspotClick={handleHotspotClick}
          className="h-full w-full"
          height="100vh"
        />
      </div>
    </>
  );
};

export default EmbedPanoramaPage;
