// frontend\src\pages\index.tsx

import React, { useState, useEffect } from "react";
import Head from "next/head";
import { GetStaticProps } from "next";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import dynamic from "next/dynamic";
import axios from "axios";
import {
  MousePointer2,
  RotateCcw,
  ZoomIn,
  ImageIcon,
  LogIn,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { LanguageToggle } from "@/components/LanguageToggle";
import { useAppTranslation } from "@/hooks/useAppTranslation";

// Dynamically import View360PanoramaViewer to avoid SSR issues
const PanoramaViewer = dynamic(
  () => import("@/components/View360PanoramaViewer"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-96 bg-muted rounded-lg">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading 360° viewer...</p>
        </div>
      </div>
    ),
  }
);

import { Hotspot, Panorama } from "@/types/hotspot";

export default function Home() {
  const { t } = useAppTranslation();
  const [panorama, setPanorama] = useState<Panorama | null>(null);
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch the first active panorama and its hotspots
      const panoramasResponse = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas`
      );

      const panoramas = panoramasResponse.data.data.panoramas || [];
      const activePanorama = panoramas.find((p: Panorama) => p.isActive);

      if (activePanorama) {
        setPanorama(activePanorama);

        // Fetch hotspots for this panorama using the public endpoint
        const hotspotsResponse = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/panorama/${activePanorama.id}`
        );

        setHotspots(hotspotsResponse.data.data.hotspots || []);

        // Track panorama view
        try {
          await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/api/analytics/view`,
            { panoramaId: activePanorama.id }
          );
        } catch (error) {
          // Silently fail view tracking to not affect user experience
          console.log("Failed to track view:", error);
        }
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
      setError(t("homepage.loading.failed", "Failed to load panorama data"));
    } finally {
      setLoading(false);
    }
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    console.log("Hotspot clicked:", hotspot);
  };

  const instructions = [
    {
      icon: MousePointer2,
      text: t(
        "homepage.navigation.clickHotspots",
        "Click hotspots for more information"
      ),
      color: "text-blue-600",
    },
    {
      icon: RotateCcw,
      text: t("homepage.navigation.dragToLook", "Drag to look around"),
      color: "text-green-600",
    },
    {
      icon: ZoomIn,
      text: t("homepage.navigation.scrollToZoom", "Scroll to zoom"),
      color: "text-purple-600",
    },
  ];

  // Loading state
  if (loading) {
    return (
      <>
        <Head>
          <title>{t("homepage.loading.title", "IduView - Loading...")}</title>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
          />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground">
              {t("homepage.loading.panorama", "Loading panorama...")}
            </p>
          </div>
        </main>
      </>
    );
  }

  // Empty state - no panoramas
  if (!panorama) {
    return (
      <>
        <Head>
          <title>{t("title", "IduView - 360° Panorama Viewer")}</title>
          <meta
            name="description"
            content={t(
              "description",
              "Interactive 360° panorama viewer with hotspots"
            )}
          />
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
          />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main className="min-h-screen bg-background">
          {/* Language Toggle - Top Right */}
          <div className="absolute top-4 right-4 z-10">
            <LanguageToggle />
          </div>

          <div className="container mx-auto px-4 py-16">
            <div className="text-center space-y-8">
              {/* Header */}
              <div className="space-y-4">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <span className="text-primary-foreground font-bold text-lg">
                      I
                    </span>
                  </div>
                  <h1 className="text-4xl font-bold tracking-tight">IduView</h1>
                </div>
                <h2 className="text-3xl font-bold text-foreground">
                  {t("title", "360° Panorama Viewer")}
                </h2>
              </div>

              {/* Empty State */}
              <div className="max-w-md mx-auto space-y-6">
                <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto">
                  <ImageIcon className="h-12 w-12 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">
                    {t("homepage.noPanoramas", "No Panoramas Available")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t(
                      "homepage.noPanoramasDescription",
                      "No panoramas have been added yet. You need to log in as an admin to add panoramas and hotspots."
                    )}
                  </p>
                </div>
                <Button asChild>
                  <a href="/login">
                    <LogIn className="mr-2 h-4 w-4" />
                    {t("navigation.adminLogin", "Admin Login")}
                  </a>
                </Button>
              </div>
            </div>
          </div>
        </main>
      </>
    );
  }

  // Main content with panorama
  return (
    <>
      <Head>
        <title>{`IduView - ${panorama.title}`}</title>
        <meta
          name="description"
          content={
            panorama.description ||
            t(
              "homepage.meta.description",
              "Interactive 360° panorama with hotspots"
            )
          }
        />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-background">
        {/* Language Toggle - Top Right */}
        <div className="absolute top-4 right-4 z-10">
          <LanguageToggle />
        </div>

        {/* Header */}
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8 space-y-4">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">
                  I
                </span>
              </div>
              <h1 className="text-4xl font-bold tracking-tight">IduView</h1>
            </div>
            <h2 className="text-3xl font-bold text-foreground">
              {panorama.title}
            </h2>
            {panorama.description && (
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                {panorama.description}
              </p>
            )}
            <Badge variant="secondary" className="text-sm">
              {t("homepage.badge.experience", "Interactive 360° Experience")}
              {hotspots.length > 0 &&
                ` • ${hotspots.length} ${
                  hotspots.length === 1
                    ? t("homepage.badge.hotspot", "Hotspot")
                    : t("homepage.badge.hotspots", "Hotspots")
                }`}
            </Badge>
          </div>
        </div>

        {/* Main Viewer with side margins */}
        <div className="px-6">
          <PanoramaViewer
            panoramaUrl={`${process.env.NEXT_PUBLIC_API_URL}${panorama.imageUrl}`}
            hotspots={hotspots}
            onHotspotClick={handleHotspotClick}
            height="90vh"
            className="w-full rounded-lg overflow-hidden shadow-xl"
          />
        </div>

        <div className="container mx-auto px-4 py-8">
          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">
                {t("homepage.navigation.title", "How to Navigate")}
              </CardTitle>
              <CardDescription className="text-center">
                {t(
                  "homepage.navigation.description",
                  "Use these controls to explore the 360° environment"
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {instructions.map((instruction, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-4 rounded-lg bg-muted/50"
                  >
                    <div
                      className={`p-2 rounded-full bg-background ${instruction.color}`}
                    >
                      <instruction.icon className="h-4 w-4" />
                    </div>
                    <span className="text-sm font-medium">
                      {instruction.text}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <div className="mt-8 grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(
                    "homepage.features.interactiveHotspots",
                    "Interactive Hotspots"
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {hotspots.length > 0
                    ? t(
                        "homepage.features.interactiveHotspotsDesc",
                        "Discover detailed information by clicking on the {{count}} interactive hotspot{{plural}} in this panorama.",
                        {
                          count: hotspots.length,
                          plural: hotspots.length !== 1 ? "s" : "",
                        }
                      )
                    : t(
                        "homepage.features.interactiveHotspotsDescEmpty",
                        "Interactive hotspots can be added to provide detailed information about specific areas or features."
                      )}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t(
                    "homepage.features.interactiveNavigation",
                    "Interactive Navigation"
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {t(
                    "homepage.features.interactiveNavigationDesc",
                    "Click and drag to explore the 360° environment with smooth navigation controls. Drag to look around and scroll to zoom."
                  )}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {t("homepage.features.immersiveViewing", "Immersive Viewing")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {t(
                    "homepage.features.immersiveViewingDesc",
                    "Experience high-quality panoramic imagery with interactive elements that bring the environment to life."
                  )}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Admin Link */}
          <div className="mt-8 text-center">
            <Button variant="outline" asChild>
              <a href="/login">
                <LogIn className="mr-2 h-4 w-4" />
                {t("navigation.adminAccess", "Admin Access")}
              </a>
            </Button>
          </div>
        </div>
      </main>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? "en", ["common"])),
    },
  };
};
