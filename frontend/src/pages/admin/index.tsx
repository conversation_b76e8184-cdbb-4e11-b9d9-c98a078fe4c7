// frontend\src\pages\admin\index.tsx

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import axios from "axios";
import { toast } from "react-hot-toast";
import {
  Image,
  MapPin,
  Languages,
  Eye,
  Plus,
  TrendingUp,
  Activity,
  Users,
  Calendar,
  BarChart3,
} from "lucide-react";
import { useAppTranslation } from "@/hooks/useAppTranslation";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

interface DashboardStats {
  panoramas: number;
  hotspots: number;
  languages: number;
  activeLanguages: number;
  viewsToday: number;
}

interface RecentActivity {
  id: string;
  type: string;
  title: string;
  description?: string;
  createdAt: string;
  panorama?: {
    id: string;
    title: string;
  };
}

const AdminDashboard: React.FC = () => {
  const { t } = useAppTranslation();
  const [stats, setStats] = useState<DashboardStats>({
    panoramas: 0,
    hotspots: 0,
    languages: 0,
    activeLanguages: 0,
    viewsToday: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      // Fetch dashboard analytics
      const analyticsRes = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/analytics/dashboard`,
        { headers }
      );

      const { stats: analyticsStats, recentActivities } =
        analyticsRes.data.data;

      setStats({
        panoramas: analyticsStats.panoramas,
        hotspots: analyticsStats.hotspots,
        languages: analyticsStats.languages,
        activeLanguages: analyticsStats.activeLanguages,
        viewsToday: analyticsStats.viewsToday,
      });

      setRecentActivity(recentActivities);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error(
        t("admin.dashboardPage.loadError", "Failed to load dashboard data")
      );
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      name: t("admin.dashboardPage.stats.totalPanoramas", "Total Panoramas"),
      value: stats.panoramas,
      icon: Image,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      href: "/admin/panoramas",
      change: t("admin.dashboardPage.stats.changePositive12", "+12%"),
      changeType: "positive" as const,
    },
    {
      name: t("admin.dashboardPage.stats.totalHotspots", "Total Hotspots"),
      value: stats.hotspots,
      icon: MapPin,
      color: "text-green-600",
      bgColor: "bg-green-50",
      href: "/admin/hotspots",
      change: t("admin.dashboardPage.stats.changePositive8", "+8%"),
      changeType: "positive" as const,
    },
    {
      name: t("admin.dashboardPage.stats.languages", "Languages"),
      value: `${stats.activeLanguages}/${stats.languages}`,
      icon: Languages,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      href: "/admin/languages",
      change: `3 ${t("admin.dashboardPage.stats.active", "active")}`,
      changeType: "neutral" as const,
    },
    {
      name: t("admin.dashboardPage.stats.viewsToday", "Views Today"),
      value: stats.viewsToday,
      icon: Eye,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      href: "#",
      change: t("admin.dashboardPage.stats.today", "Today"),
      changeType: "neutral" as const,
    },
  ];

  const quickActions = [
    {
      name: t(
        "admin.dashboardPage.quickActions.uploadPanorama",
        "Upload Panorama"
      ),
      description: t(
        "admin.dashboardPage.quickActions.uploadPanoramaDesc",
        "Add a new 360° panorama"
      ),
      icon: Image,
      href: "/admin/panoramas",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      name: t("admin.dashboardPage.quickActions.addHotspot", "Add Hotspot"),
      description: t(
        "admin.dashboardPage.quickActions.addHotspotDesc",
        "Create interactive hotspots"
      ),
      icon: MapPin,
      href: "/admin/hotspots",
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      name: t(
        "admin.dashboardPage.quickActions.manageLanguages",
        "Manage Languages"
      ),
      description: t(
        "admin.dashboardPage.quickActions.manageLanguagesDesc",
        "Configure language settings"
      ),
      icon: Languages,
      href: "/admin/languages",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
  ];

  if (loading) {
    return (
      <AdminLayout title={t("admin.dashboard", "Dashboard")}>
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">
              {t("admin.dashboardPage.loading", "Loading dashboard...")}
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={t("admin.dashboard", "Dashboard")}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex flex-col space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            {t("admin.dashboardPage.welcome", "Welcome back!")}
          </h2>
          <p className="text-muted-foreground">
            {t(
              "admin.dashboardPage.subtitle",
              "Here's what's happening with your 360° panorama system today."
            )}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statCards.map((card) => (
            <Card key={card.name} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {card.name}
                </CardTitle>
                <div className={`p-2 rounded-md ${card.bgColor}`}>
                  <card.icon className={`h-4 w-4 ${card.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{card.value}</div>
                <p className="text-xs text-muted-foreground">
                  {card.change}{" "}
                  {t(
                    "admin.dashboardPage.stats.fromLastMonth",
                    "from last month"
                  )}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              {t("admin.dashboardPage.quickActions.title", "Quick Actions")}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.dashboardPage.quickActions.subtitle",
                "Get started with common tasks"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {quickActions.map((action) => (
                <Button
                  key={action.name}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start space-y-2"
                  asChild
                >
                  <a href={action.href}>
                    <div className={`p-2 rounded-md ${action.bgColor}`}>
                      <action.icon className={`h-4 w-4 ${action.color}`} />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{action.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                  </a>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              {t("admin.dashboardPage.recentActivity.title", "Recent Activity")}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.dashboardPage.recentActivity.subtitle",
                "Latest updates and changes"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.length === 0 ? (
                <div className="text-center py-8">
                  <Activity className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-2 text-sm font-semibold">
                    {t(
                      "admin.dashboardPage.recentActivity.noActivity",
                      "No recent activity"
                    )}
                  </h3>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {t(
                      "admin.dashboardPage.recentActivity.noActivityDesc",
                      "Activity will appear here as you use the system."
                    )}
                  </p>
                </div>
              ) : (
                recentActivity.slice(0, 10).map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-4"
                  >
                    <div
                      className={`p-2 rounded-full ${
                        activity.type.includes("panorama")
                          ? "bg-blue-50"
                          : activity.type.includes("hotspot")
                          ? "bg-green-50"
                          : activity.type.includes("content")
                          ? "bg-yellow-50"
                          : activity.type.includes("language")
                          ? "bg-purple-50"
                          : "bg-gray-50"
                      }`}
                    >
                      {activity.type.includes("panorama") && (
                        <Image className="h-4 w-4 text-blue-600" />
                      )}
                      {activity.type.includes("hotspot") && (
                        <MapPin className="h-4 w-4 text-green-600" />
                      )}
                      {activity.type.includes("content") && (
                        <TrendingUp className="h-4 w-4 text-yellow-600" />
                      )}
                      {activity.type.includes("language") && (
                        <Languages className="h-4 w-4 text-purple-600" />
                      )}
                      {activity.type.includes("user") && (
                        <Users className="h-4 w-4 text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {activity.title}
                      </p>
                      {activity.description && (
                        <p className="text-xs text-muted-foreground">
                          {activity.description}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
