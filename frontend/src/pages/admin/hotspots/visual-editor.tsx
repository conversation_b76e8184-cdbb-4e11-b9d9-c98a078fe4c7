// frontend\src\pages\admin\hotspots\visual-editor.tsx

import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import AdminLayout from "@/components/AdminLayout";
import VisualHotspotEditor from "@/components/VisualHotspotEditor";
import { EnhancedHotspotContentForm } from "@/components/EnhancedHotspotContentForm";
import axios from "axios";
import { toast } from "react-hot-toast";
import { ArrowLeft, Image as ImageIcon } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAppTranslation } from "@/hooks/useAppTranslation";

import { Hotspot, Panorama } from "@/types/hotspot";

interface Language {
  code: string;
  name: string;
  nativeName: string;
  isActive: boolean;
}

// Schema will be created inside the component to access translations

const VisualHotspotEditorPage: React.FC = () => {
  const { t } = useAppTranslation();
  const router = useRouter();
  const { panoramaId: urlPanoramaId } = router.query;

  const [panoramas, setPanoramas] = useState<Panorama[]>([]);
  const [selectedPanoramaId, setSelectedPanoramaId] = useState<string>("");
  const [panorama, setPanorama] = useState<Panorama | null>(null);
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);
  const [isPlacingMode, setIsPlacingMode] = useState(false);
  const [showContentDialog, setShowContentDialog] = useState(false);
  const [pendingHotspotPosition, setPendingHotspotPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [editingHotspot, setEditingHotspot] = useState<Hotspot | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    // Set initial panorama from URL if provided
    if (urlPanoramaId && typeof urlPanoramaId === "string") {
      setSelectedPanoramaId(urlPanoramaId);
    }
  }, [urlPanoramaId]);

  useEffect(() => {
    if (selectedPanoramaId) {
      fetchPanoramaData();
    }
  }, [selectedPanoramaId]);

  const fetchInitialData = async () => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      const [panoramasRes, languagesRes] = await Promise.all([
        axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/panoramas`, {
          headers,
        }),
        axios.get(`${process.env.NEXT_PUBLIC_API_URL}/api/languages`, {
          headers,
        }),
      ]);

      setPanoramas(panoramasRes.data.data.panoramas || []);
      setLanguages(languagesRes.data.data.languages || []);
    } catch (error) {
      toast.error(
        t("admin.visualEditorPage.messages.fetchFailed", "Failed to fetch data")
      );
      router.push("/admin/hotspots");
    } finally {
      setLoading(false);
    }
  };

  const fetchPanoramaData = async () => {
    if (!selectedPanoramaId) return;

    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      const [panoramaRes, hotspotsRes] = await Promise.all([
        axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/api/panoramas/${selectedPanoramaId}`,
          { headers }
        ),
        axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/panorama/${selectedPanoramaId}`,
          { headers }
        ),
      ]);

      setPanorama(panoramaRes.data.data.panorama);
      setHotspots(hotspotsRes.data.data.hotspots || []);
    } catch (error) {
      toast.error(
        t(
          "admin.visualEditorPage.messages.fetchFailed",
          "Failed to fetch panorama data"
        )
      );
      setPanorama(null);
      setHotspots([]);
    }
  };

  const handleHotspotPlace = async (xPosition: number, yPosition: number) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      console.log("🚀 Creating hotspot at:", { xPosition, yPosition });

      // Create hotspot with default values
      const hotspotData = {
        panoramaId: selectedPanoramaId,
        xPosition,
        yPosition,
        iconType: "info",
        iconColor: "#007bff",
      };

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots`,
        hotspotData,
        { headers }
      );

      const newHotspot = response.data.data.hotspot;
      console.log("✅ Hotspot created:", newHotspot);

      // Update hotspots list immediately
      setHotspots([...hotspots, newHotspot]);

      // Store position for content creation
      setPendingHotspotPosition({ x: xPosition, y: yPosition });
      setEditingHotspot(newHotspot);
      setShowContentDialog(true);
      toast.success(
        t(
          "admin.visualEditorPage.messages.hotspotPlaced",
          "Hotspot placed! Now add content."
        )
      );
    } catch (error: any) {
      console.error("❌ Failed to place hotspot:", error);
      const message =
        error.response?.data?.error?.message ||
        t(
          "admin.visualEditorPage.messages.placeFailed",
          "Failed to place hotspot"
        );
      toast.error(message);
    }
  };

  const handleHotspotPlaced = () => {
    // Force refresh of data after hotspot is placed
    fetchPanoramaData();
  };

  const handleHotspotEdit = (hotspot: Hotspot) => {
    setEditingHotspot(hotspot);
    setShowContentDialog(true);
  };

  const onSubmitContent = async (data: any) => {
    setIsSubmitting(true);
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      if (editingHotspot?.content && editingHotspot.content.length > 0) {
        // Update existing content
        const contentId = editingHotspot.content[0].id;
        await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content/${contentId}`,
          data,
          { headers }
        );
        toast.success(
          t(
            "admin.visualEditorPage.messages.contentUpdated",
            "Content updated successfully"
          )
        );
      } else {
        // Create new content
        const payload = { ...data, hotspotId: editingHotspot?.id };
        await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/content`,
          payload,
          { headers }
        );
        toast.success(
          t(
            "admin.visualEditorPage.messages.contentCreated",
            "Content created successfully"
          )
        );
      }

      fetchPanoramaData();
      handleCloseContentDialog();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t(
          "admin.visualEditorPage.messages.operationFailed",
          "Operation failed"
        );
      toast.error(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseContentDialog = () => {
    setShowContentDialog(false);
    setEditingHotspot(null);
    setPendingHotspotPosition(null);
  };

  const handleDeleteHotspot = async (hotspotId: string) => {
    if (
      !confirm(
        t(
          "admin.visualEditorPage.messages.deleteConfirm",
          "Are you sure you want to delete this hotspot?"
        )
      )
    )
      return;

    try {
      const token = localStorage.getItem("token");
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/hotspots/${hotspotId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast.success(
        t(
          "admin.visualEditorPage.messages.deleteSuccess",
          "Hotspot deleted successfully"
        )
      );
      setHotspots(hotspots.filter((h) => h.id !== hotspotId));
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t("admin.visualEditorPage.messages.deleteFailed", "Delete failed");
      toast.error(message);
    }
  };

  if (loading) {
    return (
      <AdminLayout
        title={t("admin.visualEditorPage.title", "Visual Hotspot Editor")}
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">
              {t("admin.visualEditorPage.loading", "Loading panorama...")}
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Show panorama selection if no panorama is selected
  if (!selectedPanoramaId || !panorama) {
    return (
      <AdminLayout
        title={t("admin.visualEditorPage.title", "Visual Hotspot Editor")}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push("/admin/hotspots")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("admin.visualEditorPage.backToHotspots", "Back to Hotspots")}
              </Button>
              <div>
                <h2 className="text-2xl font-bold">
                  {t("admin.visualEditorPage.title", "Visual Hotspot Editor")}
                </h2>
                <p className="text-muted-foreground">
                  {t(
                    "admin.visualEditorPage.selectPanoramaInstruction",
                    "Select a panorama to start editing hotspots visually"
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Panorama Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {t(
                  "admin.visualEditorPage.panoramaSelection.title",
                  "Select Panorama"
                )}
              </CardTitle>
              <CardDescription>
                {t(
                  "admin.visualEditorPage.panoramaSelection.description",
                  "Choose a panorama to place and edit hotspots"
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Label
                  htmlFor="panorama-select"
                  className="text-sm font-medium"
                >
                  {t(
                    "admin.visualEditorPage.panoramaSelection.label",
                    "Panorama:"
                  )}
                </Label>
                <Select
                  value={selectedPanoramaId}
                  onValueChange={setSelectedPanoramaId}
                >
                  <SelectTrigger className="w-[400px]">
                    <SelectValue
                      placeholder={t(
                        "admin.visualEditorPage.panoramaSelection.placeholder",
                        "Select a panorama"
                      )}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {panoramas.map((panorama) => (
                      <SelectItem key={panorama.id} value={panorama.id}>
                        <div className="flex items-center space-x-2">
                          <ImageIcon className="h-4 w-4" />
                          <span>{panorama.title}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {panoramas.length === 0 && (
                <p className="text-sm text-muted-foreground mt-4">
                  {t(
                    "admin.visualEditorPage.panoramaSelection.noPanoramas",
                    "No panoramas available. Please create a panorama first."
                  )}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={t(
        "admin.visualEditorPage.titleWithPanorama",
        "Visual Editor - {panorama}",
        { panorama: panorama.title }
      )}
    >
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.push("/admin/hotspots")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("admin.visualEditorPage.backToHotspots", "Back to Hotspots")}
            </Button>
            <div>
              <h2 className="text-2xl font-bold">{panorama.title}</h2>
              <p className="text-muted-foreground">
                {t(
                  "admin.visualEditorPage.instruction",
                  "Click on the panorama to place hotspots visually"
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Visual Editor */}
        <VisualHotspotEditor
          panoramaUrl={`${process.env.NEXT_PUBLIC_API_URL}${panorama.imageUrl}`}
          existingHotspots={hotspots}
          onHotspotPlace={handleHotspotPlace}
          onHotspotEdit={handleHotspotEdit}
          onHotspotDelete={handleDeleteHotspot}
          isPlacingMode={isPlacingMode}
          onTogglePlacingMode={() => setIsPlacingMode(!isPlacingMode)}
          onHotspotPlaced={handleHotspotPlaced}
        />

        {/* Enhanced Content Form */}
        <EnhancedHotspotContentForm
          isOpen={showContentDialog}
          onClose={handleCloseContentDialog}
          onSubmit={onSubmitContent}
          initialData={
            editingHotspot?.content && editingHotspot.content.length > 0
              ? (editingHotspot.content[0] as any)
              : undefined
          }
          languages={languages.filter((lang) => lang.isActive)}
          isSubmitting={isSubmitting}
          title={
            editingHotspot?.content && editingHotspot.content.length > 0
              ? t(
                  "admin.visualEditorPage.contentDialog.editTitle",
                  "Edit Hotspot Content"
                )
              : t(
                  "admin.visualEditorPage.contentDialog.addTitle",
                  "Add Hotspot Content"
                )
          }
          description={
            pendingHotspotPosition
              ? t(
                  "admin.visualEditorPage.contentDialog.position",
                  "Position: {x}%, {y}% - Configure content and styling for this hotspot.",
                  {
                    x: pendingHotspotPosition.x.toFixed(1),
                    y: pendingHotspotPosition.y.toFixed(1),
                  }
                )
              : t(
                  "admin.visualEditorPage.contentDialog.editDescription",
                  "Edit the content and styling that will be displayed when users click this hotspot."
                )
          }
        />
      </div>
    </AdminLayout>
  );
};

export default VisualHotspotEditorPage;
