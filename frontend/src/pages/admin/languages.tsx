// frontend\src\pages\admin\languages.tsx

import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  Plus,
  Edit,
  Trash2,
  Check,
  X,
  Languages,
  Globe,
  Users,
  FileText,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { useAppTranslation } from "@/hooks/useAppTranslation";

interface Language {
  code: string;
  name: string;
  nativeName: string;
  isActive: boolean;
  isDefault: boolean;
  _count: {
    hotspotContent: number;
  };
}

// Schema will be created inside the component to access translations

type LanguageFormData = {
  code: string;
  name: string;
  nativeName: string;
  isDefault: boolean;
};

const LanguagesPage: React.FC = () => {
  const { t } = useAppTranslation();
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingLanguage, setEditingLanguage] = useState<Language | null>(null);

  // Create schema with translations
  const languageSchema = yup.object({
    code: yup
      .string()
      .matches(
        /^[a-z]{2}(-[A-Z]{2})?$/,
        t(
          "admin.languagesPage.form.validation.codeInvalid",
          "Invalid language code format"
        )
      )
      .required(),
    name: yup
      .string()
      .required(
        t(
          "admin.languagesPage.form.validation.nameRequired",
          "Name is required"
        )
      ),
    nativeName: yup
      .string()
      .required(
        t(
          "admin.languagesPage.form.validation.nativeNameRequired",
          "Native name is required"
        )
      ),
    isDefault: yup.boolean().default(false),
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<LanguageFormData>({
    resolver: yupResolver(languageSchema),
  });

  const isDefault = watch("isDefault");

  useEffect(() => {
    fetchLanguages();
  }, []);

  const fetchLanguages = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/api/languages`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setLanguages(response.data.data.languages);
    } catch (error) {
      toast.error(
        t(
          "admin.languagesPage.messages.fetchFailed",
          "Failed to fetch languages"
        )
      );
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: LanguageFormData) => {
    try {
      const token = localStorage.getItem("token");
      const headers = { Authorization: `Bearer ${token}` };

      if (editingLanguage) {
        await axios.put(
          `${process.env.NEXT_PUBLIC_API_URL}/api/languages/${editingLanguage.code}`,
          data,
          { headers }
        );
        toast.success(
          t(
            "admin.languagesPage.messages.updateSuccess",
            "Language updated successfully"
          )
        );
      } else {
        await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/languages`,
          data,
          { headers }
        );
        toast.success(
          t(
            "admin.languagesPage.messages.createSuccess",
            "Language created successfully"
          )
        );
      }

      fetchLanguages();
      handleCloseForm();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t("admin.languagesPage.messages.operationFailed", "Operation failed");
      toast.error(message);
    }
  };

  const handleEdit = (language: Language) => {
    setEditingLanguage(language);
    setValue("code", language.code);
    setValue("name", language.name);
    setValue("nativeName", language.nativeName);
    setValue("isDefault", language.isDefault);
    setShowForm(true);
  };

  const handleDelete = async (code: string) => {
    if (
      !confirm(
        t(
          "admin.languagesPage.messages.deleteConfirm",
          "Are you sure you want to delete this language?"
        )
      )
    )
      return;

    try {
      const token = localStorage.getItem("token");
      await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}/api/languages/${code}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast.success(
        t(
          "admin.languagesPage.messages.deleteSuccess",
          "Language deleted successfully"
        )
      );
      fetchLanguages();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t("admin.languagesPage.messages.deleteFailed", "Delete failed");
      toast.error(message);
    }
  };

  const toggleActive = async (code: string, isActive: boolean) => {
    try {
      const token = localStorage.getItem("token");
      await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}/api/languages/${code}`,
        { isActive: !isActive },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      const statusText = !isActive
        ? t("admin.languagesPage.messages.activated", "activated")
        : t("admin.languagesPage.messages.deactivated", "deactivated");
      toast.success(`Language ${statusText}`);
      fetchLanguages();
    } catch (error: any) {
      const message =
        error.response?.data?.error?.message ||
        t("admin.languagesPage.messages.updateFailed", "Update failed");
      toast.error(message);
    }
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditingLanguage(null);
    reset();
  };

  const stats = {
    total: languages.length,
    active: languages.filter((l) => l.isActive).length,
    totalContent: languages.reduce(
      (sum, l) => sum + l._count.hotspotContent,
      0
    ),
  };

  if (loading) {
    return (
      <AdminLayout title={t("admin.languagesPage.title", "Languages")}>
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-sm text-muted-foreground">
              {t("admin.languagesPage.loading", "Loading languages...")}
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={t("admin.languagesPage.title", "Languages")}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">
                {t("admin.languagesPage.title", "Languages")}
              </h2>
              <p className="text-muted-foreground text-sm sm:text-base">
                {t(
                  "admin.languagesPage.subtitle",
                  "Manage language locales for hotspot content"
                )}
              </p>
            </div>
            <div className="px-3 sm:px-0">
              <Dialog open={showForm} onOpenChange={setShowForm}>
                <DialogTrigger asChild>
                  <Button className="w-full sm:w-auto">
                    <Plus className="mr-2 h-4 w-4" />
                    <span className="sm:inline">
                      {t("admin.languagesPage.addLanguage", "Add Language")}
                    </span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-[calc(100vw-24px)] sm:max-w-[425px] max-h-[90vh] overflow-y-auto rounded-lg">
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                      <DialogTitle>
                        {editingLanguage
                          ? t(
                              "admin.languagesPage.form.editTitle",
                              "Edit Language"
                            )
                          : t(
                              "admin.languagesPage.form.addTitle",
                              "Add Language"
                            )}
                      </DialogTitle>
                      <DialogDescription>
                        {editingLanguage
                          ? t(
                              "admin.languagesPage.form.editDescription",
                              "Update the language settings below."
                            )
                          : t(
                              "admin.languagesPage.form.addDescription",
                              "Add a new language locale for your content."
                            )}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="space-y-2">
                        <Label htmlFor="code">
                          {t(
                            "admin.languagesPage.form.codeLabel",
                            "Language Code"
                          )}
                        </Label>
                        <Input
                          id="code"
                          placeholder={t(
                            "admin.languagesPage.form.codePlaceholder",
                            "e.g., en, da, de-DE"
                          )}
                          disabled={!!editingLanguage}
                          {...register("code")}
                        />
                        {errors.code && (
                          <p className="text-sm text-destructive">
                            {errors.code.message}
                          </p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="name">
                          {t("admin.languagesPage.form.nameLabel", "Name")}
                        </Label>
                        <Input
                          id="name"
                          placeholder={t(
                            "admin.languagesPage.form.namePlaceholder",
                            "e.g., English"
                          )}
                          {...register("name")}
                        />
                        {errors.name && (
                          <p className="text-sm text-destructive">
                            {errors.name.message}
                          </p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="nativeName">
                          {t(
                            "admin.languagesPage.form.nativeNameLabel",
                            "Native Name"
                          )}
                        </Label>
                        <Input
                          id="nativeName"
                          placeholder={t(
                            "admin.languagesPage.form.nativeNamePlaceholder",
                            "e.g., English"
                          )}
                          {...register("nativeName")}
                        />
                        {errors.nativeName && (
                          <p className="text-sm text-destructive">
                            {errors.nativeName.message}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isDefault"
                          checked={isDefault}
                          onCheckedChange={(checked) =>
                            setValue("isDefault", !!checked)
                          }
                        />
                        <Label htmlFor="isDefault">
                          {t(
                            "admin.languagesPage.form.defaultLabel",
                            "Set as default language"
                          )}
                        </Label>
                      </div>
                    </div>
                    <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleCloseForm}
                        className="w-full sm:w-auto"
                      >
                        {t("admin.languagesPage.form.cancel", "Cancel")}
                      </Button>
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full sm:w-auto"
                      >
                        {isSubmitting
                          ? t("admin.languagesPage.form.saving", "Saving...")
                          : editingLanguage
                          ? t("admin.languagesPage.form.update", "Update")
                          : t("admin.languagesPage.form.create", "Create")}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-3">
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t(
                  "admin.languagesPage.stats.totalLanguages",
                  "Total Languages"
                )}
              </CardTitle>
              <Languages className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.active} {t("admin.languagesPage.stats.active", "active")}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t(
                  "admin.languagesPage.stats.activeLanguages",
                  "Active Languages"
                )}
              </CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.active}</div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.languagesPage.stats.availableForContent",
                  "Available for content"
                )}
              </p>
            </CardContent>
          </Card>
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t("admin.languagesPage.stats.totalContent", "Total Content")}
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalContent}</div>
              <p className="text-xs text-muted-foreground">
                {t(
                  "admin.languagesPage.stats.hotspotContentItems",
                  "Hotspot content items"
                )}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Languages Management */}
        <Card>
          <CardHeader>
            <CardTitle>
              {t("admin.languagesPage.table.title", "Language Management")}
            </CardTitle>
            <CardDescription>
              {t(
                "admin.languagesPage.table.subtitle",
                "Configure and manage language settings for your content"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Desktop Table View */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      {t(
                        "admin.languagesPage.table.headers.language",
                        "Language"
                      )}
                    </TableHead>
                    <TableHead>
                      {t("admin.languagesPage.table.headers.code", "Code")}
                    </TableHead>
                    <TableHead>
                      {t(
                        "admin.languagesPage.table.headers.content",
                        "Content"
                      )}
                    </TableHead>
                    <TableHead>
                      {t("admin.languagesPage.table.headers.status", "Status")}
                    </TableHead>
                    <TableHead>
                      {t("admin.languagesPage.table.headers.active", "Active")}
                    </TableHead>
                    <TableHead className="text-right">
                      {t(
                        "admin.languagesPage.table.headers.actions",
                        "Actions"
                      )}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {languages.map((language) => (
                    <TableRow key={language.code}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{language.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {language.nativeName}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{language.code}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {language._count.hotspotContent}{" "}
                          {t("admin.languagesPage.table.contentItems", "items")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              language.isActive ? "default" : "secondary"
                            }
                          >
                            {language.isActive
                              ? t(
                                  "admin.languagesPage.table.statusActive",
                                  "Active"
                                )
                              : t(
                                  "admin.languagesPage.table.statusInactive",
                                  "Inactive"
                                )}
                          </Badge>
                          {language.isDefault && (
                            <Badge variant="outline">
                              {t(
                                "admin.languagesPage.table.defaultBadge",
                                "Default"
                              )}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={language.isActive}
                          onCheckedChange={() =>
                            toggleActive(language.code, language.isActive)
                          }
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(language)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(language.code)}
                            disabled={language._count.hotspotContent > 0}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Mobile Card View */}
            <div className="md:hidden space-y-4">
              {languages.map((language) => (
                <Card
                  key={language.code}
                  className="border border-border/50 shadow-sm"
                >
                  <CardContent className="p-4">
                    {/* Header with language info and status */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-semibold text-base leading-tight">
                            {language.name}
                          </h3>
                          <Badge variant="secondary" className="text-xs">
                            {language.code}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {language.nativeName}
                        </p>
                      </div>
                      <div className="flex flex-col items-end space-y-2 flex-shrink-0">
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              language.isActive ? "default" : "secondary"
                            }
                            className="text-xs"
                          >
                            {language.isActive
                              ? t(
                                  "admin.languagesPage.table.statusActive",
                                  "Active"
                                )
                              : t(
                                  "admin.languagesPage.table.statusInactive",
                                  "Inactive"
                                )}
                          </Badge>
                          {language.isDefault && (
                            <Badge variant="outline" className="text-xs">
                              {t(
                                "admin.languagesPage.table.defaultBadge",
                                "Default"
                              )}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Stats and Toggle Row */}
                    <div className="flex items-center justify-between mb-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          {language._count.hotspotContent}{" "}
                          {t(
                            "admin.languagesPage.table.contentItems",
                            "content items"
                          )}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          Active
                        </span>
                        <Switch
                          checked={language.isActive}
                          onCheckedChange={() =>
                            toggleActive(language.code, language.isActive)
                          }
                        />
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(language)}
                        className="flex items-center justify-center space-x-2"
                      >
                        <Edit className="h-4 w-4" />
                        <span>Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(language.code)}
                        disabled={language._count.hotspotContent > 0}
                        className="flex items-center justify-center space-x-2 text-destructive hover:text-destructive"
                        title={
                          language._count.hotspotContent > 0
                            ? "Cannot delete language with content"
                            : t(
                                "admin.languagesPage.actions.deleteLanguage",
                                "Delete Language"
                              )
                        }
                      >
                        <Trash2 className="h-4 w-4" />
                        <span>Delete</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default LanguagesPage;
