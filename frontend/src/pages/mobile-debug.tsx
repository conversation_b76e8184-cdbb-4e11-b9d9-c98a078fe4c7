// frontend/src/pages/mobile-debug.tsx

import React, { useState, useEffect } from "react";
import Head from "next/head";
import dynamic from "next/dynamic";

// Dynamic import to prevent SSR issues - using main View360PanoramaViewer
const View360PanoramaViewer = dynamic(
  () => import("@/components/View360PanoramaViewer"),
  {
    ssr: false,
    loading: () => (
      <div className="flex h-screen w-full items-center justify-center bg-gray-900">
        <div className="text-center text-white">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-white border-t-transparent mx-auto"></div>
          <p>Loading mobile debug...</p>
        </div>
      </div>
    ),
  }
);

const MobileDebugPage: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState<{
    userAgent: string;
    isMobile: boolean;
    viewport: { width: number; height: number };
    webglSupport: boolean;
    touchSupport: boolean;
  } | null>(null);

  useEffect(() => {
    const checkDevice = () => {
      const userAgent = navigator.userAgent || "";
      const isMobile =
        /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
          userAgent.toLowerCase()
        );

      // Check WebGL support
      const canvas = document.createElement("canvas");
      const gl =
        canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
      const webglSupport = !!gl;

      // Check touch support
      const touchSupport =
        "ontouchstart" in window || navigator.maxTouchPoints > 0;

      setDeviceInfo({
        userAgent,
        isMobile,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        webglSupport,
        touchSupport,
      });
    };

    checkDevice();
    window.addEventListener("resize", checkDevice);

    return () => {
      window.removeEventListener("resize", checkDevice);
    };
  }, []);

  // Test hotspots with correct type definition
  const testHotspots = [
    {
      id: "1",
      xPosition: 25,
      yPosition: 40,
      iconType: "default" as const,
      iconColor: "#ffffff",
      panoramaId: "1",
      content: [
        {
          title: "Core View360 Test",
          subtitle: "Mobile Implementation",
          description:
            "This hotspot tests the core View360 library implementation for mobile devices.",
          imageUrl: "",
          linkUrl: "",
          linkText: "",
          languageCode: "en",
          titleStyle: {},
          subtitleStyle: {},
          descriptionStyle: {},
          linkStyle: {
            type: "button" as const,
            size: "medium" as const,
          },
        },
      ],
    },
  ];

  const handleHotspotClick = (hotspot: any) => {
    console.log("Core View360 hotspot clicked:", hotspot);
  };

  return (
    <>
      <Head>
        <title>Mobile Debug - View360 Test</title>
        <meta
          name="description"
          content="Mobile debug page for View360 panorama viewer"
        />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content"
        />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="min-h-screen bg-gray-900 text-white">
        {/* Device Info Panel */}
        <div className="bg-gray-800 p-4 text-sm">
          <h1 className="text-lg font-bold mb-2">
            Mobile Debug - View360 Test
          </h1>
          {deviceInfo && (
            <div className="space-y-1">
              <p>
                <span className="font-semibold">Device:</span>{" "}
                {deviceInfo.isMobile ? "📱 Mobile Device" : "🖥️ Desktop Device"}
              </p>
              <p>
                <span className="font-semibold">WebGL:</span>{" "}
                {deviceInfo.webglSupport ? "✅ Supported" : "❌ Not Supported"}
              </p>
              <p>
                <span className="font-semibold">Touch:</span>{" "}
                {deviceInfo.touchSupport ? "✅ Supported" : "❌ Not Supported"}
              </p>
              <p>
                <span className="font-semibold">Viewport:</span>{" "}
                {deviceInfo.viewport.width} × {deviceInfo.viewport.height}
              </p>
              <p className="text-xs text-gray-400 truncate">
                <span className="font-semibold">UA:</span>{" "}
                {deviceInfo.userAgent}
              </p>
            </div>
          )}
        </div>

        {/* Test Instructions */}
        <div className="bg-blue-900 p-4 text-sm">
          <h2 className="font-semibold mb-2">View360 Panorama Test:</h2>
          <ul className="space-y-1 text-xs">
            <li>• This uses the @egjs/react-view360 React wrapper</li>
            <li>• Optimized for both mobile and desktop devices</li>
            <li>• Should work on all devices without black screen</li>
            <li>• Test touch drag, pinch zoom, and hotspot interaction</li>
          </ul>
        </div>

        {/* View360 Panorama Viewer */}
        <div className="h-[calc(100vh-200px)]">
          <View360PanoramaViewer
            panoramaUrl="/api/panoramas/1/image"
            hotspots={testHotspots}
            onHotspotClick={handleHotspotClick}
            height="100%"
            disableAutoplay={false}
          />
        </div>

        {/* Test Results */}
        <div className="bg-gray-800 p-4 text-xs">
          <p className="text-green-400">
            ✅ If you can see this panorama, the View360 implementation is
            working!
          </p>
          <p className="text-yellow-400 mt-1">
            ⚠️ If you see a black screen, check browser console for errors.
          </p>
          <p className="text-blue-400 mt-1">
            📱 This viewer works on both mobile and desktop devices.
          </p>
        </div>
      </main>
    </>
  );
};

export default MobileDebugPage;
