// frontend\src\pages\_app.tsx

import type { AppProps } from "next/app";
import { appWithTranslation } from "next-i18next";
import { Toaster } from "react-hot-toast";
import "@/styles/globals.css";

// Import polyfills for mobile compatibility (must be first)
import "@/utils/polyfills";

// Import View360 CSS
import "@egjs/react-view360/css/view360.min.css";

function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <Component {...pageProps} />
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "#363636",
            color: "#fff",
          },
        }}
      />
    </>
  );
}

export default appWithTranslation(App);
