// frontend/src/utils/polyfills.ts

/**
 * Polyfills for mobile and older browser compatibility
 */

// Polyfill for crypto.randomUUID for mobile browsers that don't support it
if (typeof window !== "undefined") {
  // Create a simple UUID v4 implementation
  const generateUUID = (): string => {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  // Ensure crypto object exists
  if (!window.crypto) {
    (window as any).crypto = {};
  }

  // Add randomUUID if it doesn't exist
  if (!window.crypto.randomUUID) {
    (window.crypto as any).randomUUID = generateUUID;
  }

  // Add getRandomValues if it doesn't exist
  if (!window.crypto.getRandomValues) {
    (window.crypto as any).getRandomValues = (array: Uint8Array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    };
  }

  // Also add to global scope for injected scripts
  if (typeof globalThis !== "undefined") {
    if (!globalThis.crypto) {
      (globalThis as any).crypto = window.crypto;
    }
  }
}

// Polyfill for ResizeObserver if not available
if (typeof window !== "undefined" && !window.ResizeObserver) {
  // Simple ResizeObserver polyfill for older browsers
  class ResizeObserverPolyfill {
    private callback: ResizeObserverCallback;
    private targets: Element[] = [];
    private rafId: number | null = null;

    constructor(callback: ResizeObserverCallback) {
      this.callback = callback;
    }

    observe(target: Element) {
      if (!this.targets.includes(target)) {
        this.targets.push(target);
        this.startObserving();
      }
    }

    unobserve(target: Element) {
      const index = this.targets.indexOf(target);
      if (index > -1) {
        this.targets.splice(index, 1);
        if (this.targets.length === 0) {
          this.stopObserving();
        }
      }
    }

    disconnect() {
      this.targets = [];
      this.stopObserving();
    }

    private startObserving() {
      if (this.rafId === null) {
        this.checkForChanges();
      }
    }

    private stopObserving() {
      if (this.rafId !== null) {
        cancelAnimationFrame(this.rafId);
        this.rafId = null;
      }
    }

    private checkForChanges = () => {
      const entries: ResizeObserverEntry[] = this.targets.map((target) => {
        const rect = target.getBoundingClientRect();
        return {
          target,
          contentRect: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height,
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom,
            left: rect.left,
            toJSON: () => rect.toJSON(),
          },
          borderBoxSize: [
            {
              inlineSize: rect.width,
              blockSize: rect.height,
            },
          ],
          contentBoxSize: [
            {
              inlineSize: rect.width,
              blockSize: rect.height,
            },
          ],
          devicePixelContentBoxSize: [
            {
              inlineSize: rect.width * devicePixelRatio,
              blockSize: rect.height * devicePixelRatio,
            },
          ],
        } as ResizeObserverEntry;
      });

      if (entries.length > 0) {
        this.callback(entries, this as any);
      }

      this.rafId = requestAnimationFrame(this.checkForChanges);
    };
  }

  (window as any).ResizeObserver = ResizeObserverPolyfill;
}

// Polyfill for requestIdleCallback if not available (for mobile performance)
if (typeof window !== "undefined" && !window.requestIdleCallback) {
  (window as any).requestIdleCallback = (callback: IdleRequestCallback) => {
    const start = Date.now();
    return setTimeout(() => {
      callback({
        didTimeout: false,
        timeRemaining() {
          return Math.max(0, 50 - (Date.now() - start));
        },
      });
    }, 1);
  };

  (window as any).cancelIdleCallback = (id: number) => {
    clearTimeout(id);
  };
}

// Export a function to initialize all polyfills
export const initializePolyfills = () => {
  console.log("Polyfills initialized for mobile compatibility");
};

// Auto-initialize polyfills when this module is imported
if (typeof window !== "undefined") {
  initializePolyfills();
}
