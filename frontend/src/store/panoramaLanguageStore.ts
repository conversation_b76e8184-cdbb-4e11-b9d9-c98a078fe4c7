// frontend/src/store/panoramaLanguageStore.ts

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface PanoramaLanguageState {
  contentLanguage: string;
  setContentLanguage: (language: string) => void;
}

export const usePanoramaLanguageStore = create<PanoramaLanguageState>()(
  persist(
    (set) => ({
      contentLanguage: 'en', // Default to English
      setContentLanguage: (language: string) => {
        set({ contentLanguage: language });
      },
    }),
    {
      name: 'panorama-content-language-storage',
      partialize: (state) => ({ contentLanguage: state.contentLanguage }),
    }
  )
);
