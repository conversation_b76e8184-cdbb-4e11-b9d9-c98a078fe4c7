@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

/* Photo Sphere Viewer custom styles */
.psv-container {
  @apply rounded-lg overflow-hidden;
}

.psv-navbar {
  @apply bg-black bg-opacity-75 backdrop-blur-sm;
}

.psv-button {
  @apply text-white hover:text-blue-400 transition-colors duration-200;
}

.psv-tooltip {
  @apply bg-black bg-opacity-90 text-white px-3 py-2 rounded-lg text-sm;
}

.psv-marker {
  @apply cursor-pointer transition-transform duration-200 hover:scale-110;
}

/* Loading animations */
@keyframes pulse-slow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Modal animations */
.modal-enter {
  @apply opacity-0 scale-95;
}

.modal-enter-active {
  @apply opacity-100 scale-100 transition-all duration-300 ease-out;
}

.modal-exit {
  @apply opacity-100 scale-100;
}

.modal-exit-active {
  @apply opacity-0 scale-95 transition-all duration-200 ease-in;
}

/* Rich text content styling for hotspot popups */
.rich-text-content {
  @apply text-gray-700 leading-relaxed;
}

.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
  @apply font-semibold text-gray-900 mb-2 mt-4 first:mt-0;
}

.rich-text-content h1 {
  @apply text-2xl;
}
.rich-text-content h2 {
  @apply text-xl;
}
.rich-text-content h3 {
  @apply text-lg;
}
.rich-text-content h4 {
  @apply text-base font-medium;
}
.rich-text-content h5 {
  @apply text-sm font-medium;
}
.rich-text-content h6 {
  @apply text-sm font-medium;
}

.rich-text-content p {
  @apply mb-3 last:mb-0;
}

.rich-text-content ul,
.rich-text-content ol {
  @apply mb-3 pl-6 space-y-1;
}

.rich-text-content ul {
  @apply list-disc;
}

.rich-text-content ol {
  @apply list-decimal;
}

.rich-text-content li {
  @apply mb-1;
}

.rich-text-content li p {
  @apply mb-1;
}

.rich-text-content strong,
.rich-text-content b {
  @apply font-semibold;
}

.rich-text-content em,
.rich-text-content i {
  @apply italic;
}

.rich-text-content u {
  @apply underline;
}

.rich-text-content a {
  @apply text-blue-600 hover:text-blue-800 underline;
}

.rich-text-content blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-3;
}

.rich-text-content code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.rich-text-content pre {
  @apply bg-gray-100 p-3 rounded overflow-x-auto my-3;
}

.rich-text-content pre code {
  @apply bg-transparent p-0;
}
