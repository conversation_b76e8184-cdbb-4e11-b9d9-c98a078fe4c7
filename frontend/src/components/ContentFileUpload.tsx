import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, FileText, Image, X, Loader2 } from "lucide-react";
import { useDropzone } from "react-dropzone";
import axios from "axios";

interface FileUploadResult {
  fileUrl: string;
  fileType: "image" | "pdf";
  metadata: {
    originalName?: string;
    originalUrl?: string;
    size?: number;
    mimeType?: string;
  };
}

interface ContentFileUploadProps {
  onFileUpload: (result: FileUploadResult) => void;
  onClear: () => void;
  currentFile?: {
    fileUrl: string;
    fileType: "image" | "pdf";
    metadata?: any;
  };
}

export default function ContentFileUpload({
  onFileUpload,
  onClear,
  currentFile,
}: ContentFileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      const file = acceptedFiles[0];

      // TODO: Remove this check when PDF viewer is working
      if (file.type === "application/pdf") {
        setUploadError(
          "PDF files are temporarily disabled. Please use images only."
        );
        return;
      }

      setIsUploading(true);
      setUploadError(null);

      try {
        const formData = new FormData();
        formData.append("image", file); // Use "image" field name for existing endpoint

        const token = localStorage.getItem("token");
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_API_URL}/api/upload/content-file`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "multipart/form-data",
            },
          }
        );

        console.log("📤 File upload response:", response.data);
        if (response.data.success) {
          // Transform the response to match our expected format
          const uploadData = response.data.data.original;
          const transformedData = {
            fileUrl: uploadData.path,
            // TODO: Re-enable PDF detection when viewer is working
            fileType: (file.type.startsWith("image/") ? "image" : "pdf") as
              | "image"
              | "pdf",
            metadata: {
              originalName: uploadData.originalName,
              size: uploadData.size,
              mimeType: uploadData.mimeType,
            },
          };
          console.log("✅ File upload successful:", transformedData);
          onFileUpload(transformedData);
        } else {
          console.error("❌ File upload failed:", response.data);
          setUploadError("Upload failed. Please try again.");
        }
      } catch (error: any) {
        console.error("File upload error:", error);
        setUploadError(
          error.response?.data?.error?.message ||
            "Upload failed. Please try again."
        );
      } finally {
        setIsUploading(false);
      }
    },
    [onFileUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
      // TODO: Re-enable PDF support when viewer is working
      // "application/pdf": [".pdf"],
    },
    maxFiles: 1,
    disabled: isUploading,
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (currentFile) {
    return (
      <Card className="border-2 border-dashed border-green-300 bg-green-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {currentFile.fileType === "pdf" ? (
                <FileText className="h-8 w-8 text-red-600" />
              ) : (
                <Image className="h-8 w-8 text-blue-600" />
              )}
              <div>
                <p className="font-medium">
                  {currentFile.fileType === "pdf"
                    ? "PDF Document"
                    : "Image File"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {currentFile.metadata?.originalName ||
                    currentFile.metadata?.originalUrl ||
                    "Uploaded file"}
                </p>
                {currentFile.metadata?.size && (
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(currentFile.metadata.size)}
                  </p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClear}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>File Content Mode:</strong> This content will display the
              uploaded file full-screen in the dialog, replacing title,
              description, and other content.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-blue-400 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        } ${isUploading ? "opacity-50 cursor-not-allowed" : ""}`}
      >
        <input {...getInputProps()} />
        {isUploading ? (
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-sm text-muted-foreground">Uploading...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-2">
            <Upload className="h-8 w-8 text-gray-400" />
            <p className="text-sm font-medium">
              {isDragActive
                ? "Drop the file here"
                : "Drag & drop a file here, or click to select"}
            </p>
            <p className="text-xs text-muted-foreground">
              Supports: Images (JPEG, PNG, WebP) - max 50MB
              {/* TODO: Add "and PDF files" when PDF viewer is working */}
            </p>
          </div>
        )}
      </div>

      {uploadError && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-800">{uploadError}</p>
        </div>
      )}

      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> File content mode will replace all other
          content (title, description, etc.) and display the file full-screen in
          the hotspot dialog.
        </p>
      </div>
    </div>
  );
}
