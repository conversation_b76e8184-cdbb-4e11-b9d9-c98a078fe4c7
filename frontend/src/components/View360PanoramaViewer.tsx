// frontend\src\components\View360PanoramaViewer.tsx

import React, { useMemo, useState, useEffect, useRef } from "react";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import MultiProductHotspotDialog from "@/components/MultiProductHotspotDialog";

// Import View360 and EquirectProjection directly like in your working code
import View360, { EquirectProjection } from "@egjs/react-view360";
import "@egjs/react-view360/css/view360.min.css";

import { Hotspot } from "@/types/hotspot";

interface View360PanoramaViewerProps {
  panoramaUrl: string;
  hotspots?: Hotspot[];
  language?: string;
  onHotspotClick?: (hotspot: Hotspot) => void;
  className?: string;
  height?: string;
  disableAutoplay?: boolean;
}

const View360PanoramaViewer: React.FC<View360PanoramaViewerProps> = ({
  panoramaUrl,
  hotspots = [],
  onHotspotClick,
  className = "",
  height = "100vh",
  disableAutoplay = false,
}) => {
  const { t } = useAppTranslation();
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Add View360 instance reference for hotspot.refresh() calls
  const viewerRef = useRef<any>(null);

  // Simple hotspot refresh following exact working test implementation
  useEffect(() => {
    if (viewerRef.current && viewerRef.current.hotspot) {
      viewerRef.current.hotspot.refresh();
      console.log("View360PanoramaViewer: Hotspots refreshed", hotspots.length);
    }
  }, [hotspots]); // Only refresh when hotspots array changes

  // Create projection with useMemo as recommended in the documentation
  const projection = useMemo(() => {
    console.log("🖼️ Creating projection with panoramaUrl:", panoramaUrl);
    return new EquirectProjection({
      src: panoramaUrl,
    });
  }, [panoramaUrl]);

  // Based on your database data, coordinates are already in yaw/pitch format
  // No conversion needed - use them directly as per View360 documentation
  const getYawPitch = (xPosition: number, yPosition: number) => {
    // Your database stores absolute yaw/pitch coordinates
    // Use them directly as per official View360 documentation
    return { yaw: xPosition, pitch: yPosition };
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    setSelectedHotspot(hotspot);
    setIsDialogOpen(true);

    if (onHotspotClick) {
      onHotspotClick(hotspot);
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedHotspot(null);
  };

  // No need for SSR check with direct imports

  // Get all content for the selected hotspot in the current language
  const currentLanguageProducts = selectedHotspot?.content || [];

  return (
    <>
      <style jsx>{`
        /* Minimal panorama viewer styling */
        .panorama-viewer {
          width: 100%;
          height: 80vh;
          position: relative;
        }

        /* Ensure View360 container renders properly */
        .panorama-viewer .view360-container {
          width: 100%;
          height: 100%;
        }

        .panorama-viewer .is-16by9 {
          width: 100%;
          height: 100%;
        }

        /* Basic hotspot styling - exactly like View360 documentation demo */
        .view360-hotspot {
          width: 30px;
          height: 30px;
          background: white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }
      `}</style>

      <div className="panorama-viewer">
        {!panoramaUrl ? (
          <div className="flex items-center justify-center h-full bg-gray-200">
            <p>No panorama URL provided</p>
          </div>
        ) : (
          <View360
            ref={viewerRef}
            className="is-16by9"
            projection={projection}
            hotspot={{
              zoom: false,
            }}
            autoplay={
              disableAutoplay
                ? false
                : {
                    delay: 2000,
                    speed: 0.1,
                    pauseOnHover: true,
                  }
            }
            rotate={true}
            zoom={true}
          >
            {/* Hotspot container following exact working test structure */}
            <div className="view360-hotspots">
              {hotspots.map((hotspot, idx) => {
                const { yaw, pitch } = getYawPitch(
                  hotspot.xPosition,
                  hotspot.yPosition
                );

                // Debug logging for hotspot positioning
                console.log("🎯 Viewer: Using direct yaw/pitch coordinates:", {
                  hotspotId: hotspot.id,
                  index: idx,
                  storedCoordinates: {
                    x: hotspot.xPosition,
                    y: hotspot.yPosition,
                  },
                  directYawPitch: { yaw, pitch },
                });

                const firstProduct = hotspot.content?.[0];
                const productCount = hotspot.content?.length || 0;
                const tooltipText =
                  productCount > 1
                    ? `${
                        firstProduct?.title || "Hotspot"
                      } (${productCount} products)`
                    : firstProduct?.title || "Hotspot";

                return (
                  <div
                    key={hotspot.id}
                    className="view360-hotspot"
                    data-yaw={yaw}
                    data-pitch={pitch}
                    onClick={() => handleHotspotClick(hotspot)}
                    title={tooltipText}
                  >
                    {idx + 1}
                  </div>
                );
              })}
            </div>
          </View360>
        )}

        {/* Instructions overlay - hidden on small screens */}
        <div className="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg text-sm backdrop-blur-sm hidden md:block">
          <div className="flex items-center space-x-4">
            <span>
              {t(
                "admin.panoramaViewer.controls.drag",
                "🖱️ Drag to look around"
              )}
            </span>
            <span>
              {t(
                "admin.panoramaViewer.controls.click",
                "📍 Click hotspots for info"
              )}
            </span>
            <span>
              {t("admin.panoramaViewer.controls.zoom", "🔍 Scroll to zoom")}
            </span>
          </div>
        </div>
      </div>

      {/* Multi-Product Hotspot Dialog */}
      <MultiProductHotspotDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        products={currentLanguageProducts}
        autoPlay={true}
        autoPlayInterval={5000}
      />
    </>
  );
};

export default View360PanoramaViewer;
