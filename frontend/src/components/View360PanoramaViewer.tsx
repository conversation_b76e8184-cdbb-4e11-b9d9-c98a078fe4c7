// frontend\src\components\View360PanoramaViewer.tsx

import React, { useMemo, useState, useEffect, useRef } from "react";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import MultiProductHotspotDialog from "@/components/MultiProductHotspotDialog";

// Import View360 and EquirectProjection directly like in your working code
import View360, { EquirectProjection } from "@egjs/react-view360";
import "@egjs/react-view360/css/view360.min.css";

import { Hotspot } from "@/types/hotspot";

interface View360PanoramaViewerProps {
  panoramaUrl: string;
  hotspots?: Hotspot[];
  language?: string;
  onHotspotClick?: (hotspot: Hotspot) => void;
  className?: string;
  height?: string;
  disableAutoplay?: boolean;
}

const View360PanoramaViewer: React.FC<View360PanoramaViewerProps> = ({
  panoramaUrl,
  hotspots = [],
  onHotspotClick,
  className = "",
  height = "100vh",
  disableAutoplay = false,
}) => {
  const { t } = useAppTranslation();
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Add View360 instance reference for hotspot.refresh() calls
  const viewerRef = useRef<any>(null);

  // Simple hotspot refresh following exact working test implementation
  useEffect(() => {
    if (viewerRef.current && viewerRef.current.hotspot) {
      viewerRef.current.hotspot.refresh();
      console.log("View360PanoramaViewer: Hotspots refreshed", hotspots.length);
    }
  }, [hotspots]); // Only refresh when hotspots array changes

  // Create projection with useMemo as recommended in the documentation
  const projection = useMemo(
    () =>
      new EquirectProjection({
        src: panoramaUrl,
      }),
    [panoramaUrl]
  );

  // Convert percentage positions to yaw/pitch for View360
  // This must match exactly with VisualHotspotEditor's convertPercentToYawPitch function
  const convertToYawPitch = (xPercent: number, yPercent: number) => {
    // Convert percentage to yaw/pitch
    // X: 0% = -180°, 100% = 180°
    // Y: 0% = 90°, 100% = -90°
    const yaw = (xPercent / 100) * 360 - 180;
    const pitch = 90 - (yPercent / 100) * 180;

    return { yaw, pitch };
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    setSelectedHotspot(hotspot);
    setIsDialogOpen(true);

    if (onHotspotClick) {
      onHotspotClick(hotspot);
    }
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedHotspot(null);
  };

  // No need for SSR check with direct imports

  // Get all content for the selected hotspot in the current language
  const currentLanguageProducts = selectedHotspot?.content || [];

  return (
    <>
      <style jsx>{`
        /* Exact working test styles - NO TRANSFORMS */
        .search {
          width: 24px;
          height: 24px;
          position: relative;
          cursor: pointer;
          user-select: none;
          -webkit-user-drag: none;
        }

        /* Main white hotspot circle */
        .search:before {
          position: absolute;
          content: "";
          width: 20px;
          height: 20px;
          top: 2px;
          left: 2px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border: 2px solid #343a40;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
            0 2px 4px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* First pulsing ring */
        .search:after {
          position: absolute;
          content: "";
          top: -8px;
          left: -8px;
          right: -8px;
          bottom: -8px;
          border: 2px solid rgba(52, 58, 64, 0.6);
          border-radius: 50%;
          animation: pulse-ring 2s infinite ease-out;
        }

        /* Second pulsing ring */
        .search .hotspot-inner:before {
          position: absolute;
          content: "";
          top: -32px;
          left: -32px;
          right: -32px;
          bottom: -32px;
          border: 1px solid rgba(52, 58, 64, 0.3);
          border-radius: 50%;
          animation: pulse-ring 2s infinite ease-out 0.5s;
        }

        @keyframes pulse-ring {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.7;
          }
          100% {
            transform: scale(1.6);
            opacity: 0;
          }
        }

        .search:hover:before {
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25),
            0 4px 8px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .search:hover:after,
        .search:hover .hotspot-inner:before {
          animation-duration: 1s;
        }

        /* Centered inner dot - NO TRANSFORM */
        .search .hotspot-inner {
          position: absolute;
          top: 8px;
          left: 8px;
          width: 8px;
          height: 8px;
          background: #343a40;
          border-radius: 50%;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
          z-index: 2;
        }

        /* Mobile-optimized panorama viewer styling */
        .panorama-viewer {
          width: 100vw;
          height: 80vh;
          position: relative;
          margin: 0;
          padding: 0;
          /* Critical mobile WebGL fixes */
          touch-action: none;
          -webkit-touch-callout: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          /* Force hardware acceleration */
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          /* Prevent zoom on double tap */
          -ms-touch-action: none;
          -webkit-user-drag: none;
        }

        /* Mobile panorama viewer specific class */
        .mobile-panorama-viewer {
          width: 100% !important;
          height: 100% !important;
          min-height: 300px !important;
          position: relative !important;
          /* Mobile WebGL container fixes */
          overflow: hidden !important;
          touch-action: none !important;
          -webkit-transform: translateZ(0) !important;
          transform: translateZ(0) !important;
          -webkit-backface-visibility: hidden !important;
          backface-visibility: hidden !important;
          /* Prevent mobile browser interference */
          -webkit-touch-callout: none !important;
          -webkit-user-select: none !important;
          -webkit-user-drag: none !important;
          -webkit-tap-highlight-color: transparent !important;
        }

        /* View360 container fixes */
        .panorama-viewer .view360-container,
        .mobile-panorama-viewer .view360-container {
          width: 100% !important;
          height: 100% !important;
          position: relative !important;
          overflow: hidden !important;
          touch-action: none !important;
        }

        /* Canvas specific mobile fixes */
        .panorama-viewer .view360-canvas,
        .mobile-panorama-viewer .view360-canvas {
          width: 100vw !important;
          height: 80vh !important;
          /* Ensure canvas covers the entire container */
          object-fit: cover !important;
          /* Force hardware acceleration */
          -webkit-transform: translateZ(0) !important;
          transform: translateZ(0) !important;
          -webkit-backface-visibility: hidden !important;
          backface-visibility: hidden !important;
          /* Mobile touch handling */
          touch-action: none !important;
          -webkit-touch-callout: none !important;
          /* Prevent context menu on long press */
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
          user-select: none !important;
        }

        /* Remove problematic aspect ratio for mobile */
        .panorama-viewer .is-16by9,
        .mobile-panorama-viewer .is-16by9 {
          width: 100% !important;
          height: 100% !important;
          aspect-ratio: unset !important;
          min-height: 100% !important;
          position: relative !important;
          /* Remove rounded corners on mobile */
          border-radius: 0 !important;
          overflow: hidden !important;
        }

        /* Force View360 container to fill parent */
        .panorama-viewer .view360-container,
        .mobile-panorama-viewer .view360-container {
          width: 100% !important;
          height: 100% !important;
          min-height: 100% !important;
          position: relative !important;
          overflow: hidden !important;
          border-radius: 0 !important;
        }

        /* Mobile specific media queries */
        @media (max-width: 768px) {
          .panorama-viewer,
          .mobile-panorama-viewer {
            /* Mobile full-screen optimization */
            width: 100vw !important;
            height: 80vh !important;
            min-height: 80vh !important;
            max-height: 80vh !important;
            /* Remove padding and margin */
            margin: 0 !important;
            padding: 0 !important;
            /* Full screen positioning */
            position: relative !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            /* Ensure proper sizing on small screens */
            -webkit-overflow-scrolling: touch;
          }

          .panorama-viewer .view360-canvas,
          .mobile-panorama-viewer .view360-canvas {
            /* Additional mobile optimizations */
            image-rendering: optimizeSpeed;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: optimize-contrast;
            /* Full viewport canvas */
            width: 100vw !important;
            height: 80vh !important;
            min-height: 80vh !important;
            max-height: 80vh !important;
            object-fit: cover !important;
            /* Remove any spacing */
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            border-radius: 0 !important;
          }

          /* Remove aspect ratio constraints on mobile */
          .panorama-viewer .is-16by9,
          .mobile-panorama-viewer .is-16by9 {
            aspect-ratio: unset !important;
            width: 100vw !important;
            height: 80vh !important;
            min-height: 80vh !important;
            max-height: 80vh !important;
            padding-bottom: 0 !important;
            border-radius: 0 !important;
            overflow: hidden !important;
          }

          /* Force View360 container to fill on mobile */
          .panorama-viewer .view360-container,
          .mobile-panorama-viewer .view360-container {
            width: 100vw !important;
            height: 80vh !important;
            min-height: 80vh !important;
            max-height: 80vh !important;
            border-radius: 0 !important;
            overflow: hidden !important;
          }
        }

        /* Desktop specific optimizations */
        @media (min-width: 769px) {
          .panorama-viewer,
          .mobile-panorama-viewer {
            /* Desktop uses container width and 80vh height */
            width: 100% !important;
            height: 80vh !important;
            min-height: 80vh !important;
            max-height: 80vh !important;
            /* Reset mobile positioning */
            left: auto !important;
            transform: none !important;
          }

          .panorama-viewer .view360-canvas,
          .mobile-panorama-viewer .view360-canvas {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
          }

          .panorama-viewer .is-16by9,
          .mobile-panorama-viewer .is-16by9 {
            width: 100% !important;
            height: 100% !important;
          }
        }

        /* iOS specific fixes */
        @supports (-webkit-touch-callout: none) {
          .panorama-viewer,
          .mobile-panorama-viewer {
            /* iOS Safari specific fixes */
            -webkit-overflow-scrolling: touch;
            -webkit-transform: translate3d(0, 0, 0);
          }
        }

        /* Android specific fixes */
        @media screen and (-webkit-min-device-pixel-ratio: 1) {
          .panorama-viewer,
          .mobile-panorama-viewer {
            /* Android Chrome specific optimizations */
            will-change: transform;
            -webkit-perspective: 1000px;
            perspective: 1000px;
            -webkit-transform-style: preserve-3d;
            transform-style: preserve-3d;
          }

          .panorama-viewer .view360-canvas,
          .mobile-panorama-viewer .view360-canvas {
            /* Android WebGL canvas optimizations */
            will-change: transform;
            -webkit-transform: translateZ(0) scale(1);
            transform: translateZ(0) scale(1);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            /* Force GPU compositing on Android */
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            -webkit-perspective: 1000px;
            perspective: 1000px;
          }
        }
      `}</style>

      <div
        className={`panorama-viewer ${className} relative overflow-hidden`}
        style={{
          width: "100vw",
          height: "80vh",
          minHeight: "80vh",
          maxHeight: "80vh",
          margin: 0,
          padding: 0,
          borderRadius: 0,
          border: "none",
          // Mobile full-width positioning
          ...(typeof window !== "undefined" &&
            window.innerWidth <= 768 && {
              position: "relative",
              left: "50%",
              transform: "translateX(-50%)",
              borderRadius: 0,
            }),
          // Desktop container width
          ...(typeof window !== "undefined" &&
            window.innerWidth > 768 && {
              width: "100%",
              left: "auto",
              transform: "none",
            }),
        }}
      >
        {/* Critical mobile fix: Dynamic import prevents SSR issues */}
        <View360
          ref={viewerRef}
          className="is-16by9"
          projection={projection}
          autoplay={
            disableAutoplay
              ? false
              : {
                  delay: 2000,
                  speed: 0.1,
                  pauseOnHover: true,
                }
          }
          rotate={true}
          zoom={true}
          style={{
            width: "100%",
            height: "100%",
            borderRadius: 0,
            border: "none",
            margin: 0,
            padding: 0,
          }}
        >
          {/* Hotspot container following exact working test structure */}
          <div className="view360-hotspots">
            {hotspots.map((hotspot, idx) => {
              const { yaw, pitch } = convertToYawPitch(
                hotspot.xPosition,
                hotspot.yPosition
              );

              // Debug logging for hotspot positioning
              if (idx === 0) {
                // Only log for first hotspot to avoid spam
                console.log("🎯 Viewer: Converting hotspot coordinates:", {
                  hotspotId: hotspot.id,
                  storedPercentage: {
                    x: hotspot.xPosition,
                    y: hotspot.yPosition,
                  },
                  convertedYawPitch: { yaw, pitch },
                });
              }

              const firstProduct = hotspot.content?.[0];
              const productCount = hotspot.content?.length || 0;
              const tooltipText =
                productCount > 1
                  ? `${
                      firstProduct?.title || "Hotspot"
                    } (${productCount} products)`
                  : firstProduct?.title || "Hotspot";

              return (
                <div
                  key={idx}
                  className="view360-hotspot search"
                  data-yaw={yaw}
                  data-pitch={pitch}
                  onClick={() => handleHotspotClick(hotspot)}
                  title={tooltipText}
                >
                  <div className="hotspot-inner" />
                </div>
              );
            })}
          </div>
        </View360>

        {/* Instructions overlay - hidden on small screens */}
        <div className="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg text-sm backdrop-blur-sm hidden md:block">
          <div className="flex items-center space-x-4">
            <span>
              {t(
                "admin.panoramaViewer.controls.drag",
                "🖱️ Drag to look around"
              )}
            </span>
            <span>
              {t(
                "admin.panoramaViewer.controls.click",
                "📍 Click hotspots for info"
              )}
            </span>
            <span>
              {t("admin.panoramaViewer.controls.zoom", "🔍 Scroll to zoom")}
            </span>
          </div>
        </div>
      </div>

      {/* Multi-Product Hotspot Dialog */}
      <MultiProductHotspotDialog
        isOpen={isDialogOpen}
        onClose={handleCloseDialog}
        products={currentLanguageProducts}
        autoPlay={true}
        autoPlayInterval={5000}
      />
    </>
  );
};

export default View360PanoramaViewer;
