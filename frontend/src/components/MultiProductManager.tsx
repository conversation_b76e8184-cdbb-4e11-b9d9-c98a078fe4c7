import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Eye,
  Languages,
  Package,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import { HotspotContent } from "@/types/hotspot";
import { useAppTranslation } from "@/hooks/useAppTranslation";
import { EnhancedHotspotContentForm } from "./EnhancedHotspotContentForm";

import MultiProductHotspotDialog from "./MultiProductHotspotDialog";

interface Language {
  code: string;
  name: string;
  nativeName: string;
}

interface MultiProductManagerProps {
  isOpen: boolean;
  onClose: () => void;
  hotspotTitle: string;
  products: HotspotContent[];
  languages: Language[];
  onProductAdd: (product: HotspotContent) => Promise<void>;
  onProductUpdate: (product: HotspotContent) => Promise<void>;
  onProductDelete: (productId: string) => Promise<void>;
  onProductReorder: (productIds: string[]) => Promise<void>;
}

export default function MultiProductManager({
  isOpen,
  onClose,
  hotspotTitle,
  products,
  languages,
  onProductAdd,
  onProductUpdate,
  onProductDelete,
  onProductReorder,
}: MultiProductManagerProps) {
  const { t } = useAppTranslation();
  const [selectedLanguage, setSelectedLanguage] = useState<string>("en");
  const [showContentForm, setShowContentForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<HotspotContent | null>(
    null
  );
  const [showPreview, setShowPreview] = useState(false);
  const [previewProducts, setPreviewProducts] = useState<HotspotContent[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get products for selected language, sorted by order
  const languageProducts = products
    .filter((p) => p.languageCode === selectedLanguage)
    .sort((a, b) => (a.order || 0) - (b.order || 0));

  // Get available languages that have products
  const availableLanguages = languages.filter((lang) =>
    products.some((p) => p.languageCode === lang.code)
  );

  // Set default language to first available or English
  useEffect(() => {
    if (
      availableLanguages.length > 0 &&
      !availableLanguages.find((l) => l.code === selectedLanguage)
    ) {
      setSelectedLanguage(availableLanguages[0].code);
    }
  }, [availableLanguages, selectedLanguage]);

  // Reset state when dialog opens/closes or products change
  useEffect(() => {
    if (isOpen) {
      setShowContentForm(false);
      setEditingProduct(null);
      setShowPreview(false);
    }
  }, [isOpen, products]);

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowContentForm(true);
  };

  const handleEditProduct = (product: HotspotContent) => {
    setEditingProduct(product);
    setShowContentForm(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (
      window.confirm(
        t(
          "admin.multiProduct.confirmDelete",
          "Are you sure you want to delete this product?"
        )
      )
    ) {
      await onProductDelete(productId);
    }
  };

  const handleMoveUp = async (index: number) => {
    if (index === 0) return;

    const newOrder = [...languageProducts];
    [newOrder[index - 1], newOrder[index]] = [
      newOrder[index],
      newOrder[index - 1],
    ];

    const productIds = newOrder.map((p) => p.id!);
    await onProductReorder(productIds);
  };

  const handleMoveDown = async (index: number) => {
    if (index === languageProducts.length - 1) return;

    const newOrder = [...languageProducts];
    [newOrder[index], newOrder[index + 1]] = [
      newOrder[index + 1],
      newOrder[index],
    ];

    const productIds = newOrder.map((p) => p.id!);
    await onProductReorder(productIds);
  };

  const handlePreview = () => {
    setPreviewProducts(languageProducts);
    setShowPreview(true);
  };

  const handleSubmitProduct = async (productData: HotspotContent) => {
    setIsSubmitting(true);
    try {
      const productWithOrder = {
        ...productData,
        languageCode: selectedLanguage,
        order: editingProduct ? editingProduct.order : languageProducts.length,
      };

      if (editingProduct) {
        await onProductUpdate({ ...productWithOrder, id: editingProduct.id });
      } else {
        await onProductAdd(productWithOrder);
      }

      setShowContentForm(false);
      setEditingProduct(null);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t("admin.multiProduct.title", "Manage Products")} -{" "}
              {hotspotTitle}
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col space-y-4">
            {/* Language Selector */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Languages className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {t("admin.multiProduct.language", "Language")}:
                </span>
                <div className="flex space-x-1">
                  {languages.map((lang) => (
                    <Button
                      key={lang.code}
                      variant={
                        selectedLanguage === lang.code ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => setSelectedLanguage(lang.code)}
                    >
                      {lang.code.toUpperCase()}
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {
                          products.filter((p) => p.languageCode === lang.code)
                            .length
                        }
                      </Badge>
                    </Button>
                  ))}
                </div>
              </div>

              <div className="flex space-x-2">
                {languageProducts.length > 0 && (
                  <Button variant="outline" size="sm" onClick={handlePreview}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t("admin.multiProduct.preview", "Preview Slideshow")}
                  </Button>
                )}
                <Button size="sm" onClick={handleAddProduct}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t("admin.multiProduct.addProduct", "Add Product")}
                </Button>
              </div>
            </div>

            {/* Products List */}
            <div className="flex-1 overflow-y-auto space-y-3 max-h-[60vh]">
              {languageProducts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>
                    {t(
                      "admin.multiProduct.noProducts",
                      "No products for this language yet."
                    )}
                  </p>
                  <p className="text-sm">
                    {t(
                      "admin.multiProduct.addFirst",
                      "Add your first product to get started."
                    )}
                  </p>
                </div>
              ) : (
                languageProducts.map((product, index) => (
                  <Card key={product.id} className="relative">
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          <GripVertical className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <CardTitle className="text-sm">
                              {product.title}
                            </CardTitle>
                            {product.subtitle && (
                              <p className="text-xs text-muted-foreground">
                                {product.subtitle}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Badge variant="outline" className="text-xs">
                            #{index + 1}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMoveUp(index)}
                            disabled={index === 0}
                            className="h-6 w-6 p-0"
                          >
                            <ArrowUp className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMoveDown(index)}
                            disabled={index === languageProducts.length - 1}
                            className="h-6 w-6 p-0"
                          >
                            <ArrowDown className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditProduct(product)}
                            className="h-6 w-6 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProduct(product.id!)}
                            className="h-6 w-6 p-0 text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {product.description}
                      </p>
                      {product.imageUrl && (
                        <div className="mt-2">
                          <img
                            src={product.imageUrl}
                            alt={product.title}
                            className="w-16 h-12 object-cover rounded border"
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Content Form */}
      <EnhancedHotspotContentForm
        isOpen={showContentForm}
        onClose={() => {
          setShowContentForm(false);
          setEditingProduct(null);
        }}
        onSubmit={handleSubmitProduct}
        initialData={
          editingProduct
            ? (editingProduct as Partial<HotspotContent>)
            : undefined
        }
        languages={languages}
        isSubmitting={isSubmitting}
        title={
          editingProduct
            ? t("admin.multiProduct.editProduct", "Edit Product")
            : t("admin.multiProduct.addProduct", "Add Product")
        }
        selectedLanguage={selectedLanguage}
      />

      {/* Preview Dialog */}
      <MultiProductHotspotDialog
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        products={previewProducts}
        autoPlay={true}
        autoPlayInterval={3000}
      />
    </>
  );
}
