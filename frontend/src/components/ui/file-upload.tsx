import React, { useState, useCallback, useRef } from "react";
import { Upload, X, FileImage, AlertCircle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileUpload?: (url: string, uploadData?: any) => void;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
  disabled?: boolean;
  uploadEndpoint?: string;
  preview?: boolean;
}

interface UploadState {
  file: File | null;
  preview: string | null;
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
  uploadedUrl: string | null;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  onFileUpload,
  accept = "image/*",
  maxSize = 50, // 50MB default
  className,
  disabled = false,
  uploadEndpoint,
  preview = true,
}) => {
  const [state, setState] = useState<UploadState>({
    file: null,
    preview: null,
    uploading: false,
    progress: 0,
    error: null,
    success: false,
    uploadedUrl: null,
  });

  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        resolve(`File size must be less than ${maxSize}MB`);
        return;
      }

      // Check file type
      if (accept && !file.type.match(accept.replace("*", ".*"))) {
        resolve(`Invalid file type. Expected: ${accept}`);
        return;
      }

      // For panorama images, check aspect ratio
      if (
        uploadEndpoint?.includes("panorama") &&
        file.type.startsWith("image/")
      ) {
        const img = new Image();
        img.onload = () => {
          const aspectRatio = img.width / img.height;
          if (Math.abs(aspectRatio - 2) > 0.3) {
            resolve(
              `Invalid aspect ratio (${aspectRatio.toFixed(
                2
              )}:1). Panorama images should have approximately 2:1 aspect ratio (1.7:1 to 2.3:1 accepted). Please use equirectangular projection format.`
            );
          } else {
            resolve(null);
          }
        };
        img.onerror = () => resolve("Unable to read image dimensions");
        img.src = URL.createObjectURL(file);
      } else {
        resolve(null);
      }
    });
  };

  const handleFileSelect = useCallback(
    async (file: File) => {
      const error = await validateFile(file);
      if (error) {
        setState((prev) => ({ ...prev, error, success: false }));
        return;
      }

      // Create preview for images
      let previewUrl = null;
      if (file.type.startsWith("image/") && preview) {
        previewUrl = URL.createObjectURL(file);
      }

      setState((prev) => ({
        ...prev,
        file,
        preview: previewUrl,
        error: null,
        success: false,
        uploadedUrl: null,
      }));

      onFileSelect(file);

      // Auto-upload if endpoint is provided
      if (uploadEndpoint) {
        uploadFile(file);
      }
    },
    [onFileSelect, uploadEndpoint, maxSize, accept, preview]
  );

  const uploadFile = async (file: File) => {
    if (!uploadEndpoint) return;

    setState((prev) => ({
      ...prev,
      uploading: true,
      progress: 0,
      error: null,
    }));

    try {
      const formData = new FormData();
      // Use different field names based on endpoint
      const fieldName = uploadEndpoint?.includes("panorama")
        ? "panorama"
        : "image";
      formData.append(fieldName, file);

      const token = localStorage.getItem("token");
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}${uploadEndpoint}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || "Upload failed");
      }

      const data = await response.json();
      const uploadedUrl = data.data.optimized?.path || data.data.original?.path;

      setState((prev) => ({
        ...prev,
        uploading: false,
        progress: 100,
        success: true,
        uploadedUrl,
      }));

      if (onFileUpload && uploadedUrl) {
        onFileUpload(uploadedUrl, data.data);
      }
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        uploading: false,
        progress: 0,
        error: error.message,
        success: false,
      }));
    }
  };

  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect, disabled]
  );

  const handleFileInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    [handleFileSelect]
  );

  const handleRemoveFile = () => {
    if (state.preview) {
      URL.revokeObjectURL(state.preview);
    }
    setState({
      file: null,
      preview: null,
      uploading: false,
      progress: 0,
      error: null,
      success: false,
      uploadedUrl: null,
    });
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn("w-full", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {!state.file ? (
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
            isDragOver
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/25 hover:border-primary/50",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <div className="space-y-2">
            <p className="text-sm font-medium">
              {uploadEndpoint?.includes("panorama")
                ? "Drop your 360° panorama image here, or click to browse"
                : "Drop your image here, or click to browse"}
            </p>
            <p className="text-xs text-muted-foreground">
              Supports JPEG, PNG, WebP up to {maxSize}MB
            </p>
            {uploadEndpoint?.includes("panorama") && (
              <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                <p className="font-medium">📐 Panorama Requirements:</p>
                <p>• Approximately 2:1 aspect ratio (1.7:1 to 2.3:1)</p>
                <p>• Equirectangular projection format</p>
                <p>• Covers full 360° horizontal × 180° vertical</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* File Preview */}
          <div className="border rounded-lg p-4">
            <div className="flex items-start gap-4">
              {state.preview ? (
                <img
                  src={state.preview}
                  alt="Preview"
                  className="w-20 h-20 object-cover rounded"
                />
              ) : (
                <FileImage className="w-20 h-20 text-muted-foreground" />
              )}

              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">
                  {state.file.name}
                </p>
                <p className="text-xs text-muted-foreground">
                  {(state.file.size / (1024 * 1024)).toFixed(2)} MB
                </p>

                {state.uploading && (
                  <div className="mt-2">
                    <Progress value={state.progress} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">
                      Uploading... {state.progress}%
                    </p>
                  </div>
                )}

                {state.error && (
                  <div className="flex items-center gap-1 mt-2 text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    <p className="text-xs">{state.error}</p>
                  </div>
                )}

                {state.success && (
                  <div className="flex items-center gap-1 mt-2 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <p className="text-xs">Upload successful!</p>
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveFile}
                disabled={state.uploading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Upload Button */}
          {uploadEndpoint && !state.uploading && !state.success && (
            <Button
              onClick={() => state.file && uploadFile(state.file)}
              disabled={!state.file || state.uploading}
              className="w-full"
            >
              <Upload className="mr-2 h-4 w-4" />
              Upload File
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
