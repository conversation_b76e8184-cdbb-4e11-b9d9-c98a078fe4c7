import React from 'react';

const DebugEnv: React.FC = () => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs z-50">
      <h4 className="font-bold mb-2">Debug Info:</h4>
      <div>API URL: {process.env.NEXT_PUBLIC_API_URL || 'undefined'}</div>
      <div>Frontend URL: {process.env.NEXT_PUBLIC_FRONTEND_URL || 'undefined'}</div>
      <div>Node ENV: {process.env.NODE_ENV}</div>
    </div>
  );
};

export default DebugEnv;
