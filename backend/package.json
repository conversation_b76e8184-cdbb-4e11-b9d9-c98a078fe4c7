{"name": "iduview-backend", "version": "1.0.0", "description": "Backend API for 360° panorama viewer with hotspots", "main": "dist/app.js", "scripts": {"dev": "cross-env NODE_ENV=development nodemon --exec ts-node src/app.ts", "build": "tsc", "start": "node dist/app.js", "start:dev": "cross-env NODE_ENV=development ts-node src/app.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "^5.6.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "pdf-lib": "^1.17.1", "react-dropzone": "^14.3.8", "redis": "^4.6.10", "sharp": "^0.34.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prisma": "^5.6.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "keywords": ["360", "panorama", "hotspots", "viewer", "api"], "author": "Iduna.dk", "license": "MIT", "pnpm": {"ignoredBuiltDependencies": ["sharp"]}}