import { prisma } from "./database";
import logger from "./logger";

interface ActivityData {
  type: string;
  title: string;
  description?: string;
  entityType?: string;
  entityId?: string;
  panoramaId?: string;
  userId?: string;
  metadata?: any;
}

export const logActivity = async (data: ActivityData): Promise<void> => {
  try {
    await prisma.activity.create({
      data: {
        type: data.type,
        title: data.title,
        description: data.description,
        entityType: data.entityType,
        entityId: data.entityId,
        panoramaId: data.panoramaId,
        userId: data.userId,
        metadata: data.metadata,
      },
    });
    
    logger.info(`Activity logged: ${data.type} - ${data.title}`);
  } catch (error) {
    logger.error("Failed to log activity:", error);
  }
};

// Predefined activity types
export const ActivityTypes = {
  PANORAMA_CREATED: "panorama_created",
  PANORAMA_UPDATED: "panorama_updated",
  PANORAMA_DELETED: "panorama_deleted",
  HOTSPOT_CREATED: "hotspot_created",
  HOTSPOT_UPDATED: "hotspot_updated",
  HOTSPOT_DELETED: "hotspot_deleted",
  CONTENT_CREATED: "content_created",
  CONTENT_UPDATED: "content_updated",
  CONTENT_DELETED: "content_deleted",
  LANGUAGE_CREATED: "language_created",
  LANGUAGE_UPDATED: "language_updated",
  LANGUAGE_DELETED: "language_deleted",
  USER_LOGIN: "user_login",
  USER_LOGOUT: "user_logout",
} as const;

// Helper functions for common activities
export const logPanoramaActivity = {
  created: (panoramaId: string, title: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.PANORAMA_CREATED,
      title: `Panorama "${title}" created`,
      entityType: "panorama",
      entityId: panoramaId,
      panoramaId,
      userId,
    }),

  updated: (panoramaId: string, title: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.PANORAMA_UPDATED,
      title: `Panorama "${title}" updated`,
      entityType: "panorama",
      entityId: panoramaId,
      panoramaId,
      userId,
    }),

  deleted: (panoramaId: string, title: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.PANORAMA_DELETED,
      title: `Panorama "${title}" deleted`,
      entityType: "panorama",
      entityId: panoramaId,
      panoramaId,
      userId,
    }),
};

export const logHotspotActivity = {
  created: (hotspotId: string, panoramaId: string, panoramaTitle: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.HOTSPOT_CREATED,
      title: `Hotspot added to "${panoramaTitle}"`,
      entityType: "hotspot",
      entityId: hotspotId,
      panoramaId,
      userId,
    }),

  updated: (hotspotId: string, panoramaId: string, panoramaTitle: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.HOTSPOT_UPDATED,
      title: `Hotspot updated in "${panoramaTitle}"`,
      entityType: "hotspot",
      entityId: hotspotId,
      panoramaId,
      userId,
    }),

  deleted: (hotspotId: string, panoramaId: string, panoramaTitle: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.HOTSPOT_DELETED,
      title: `Hotspot removed from "${panoramaTitle}"`,
      entityType: "hotspot",
      entityId: hotspotId,
      panoramaId,
      userId,
    }),
};

export const logContentActivity = {
  created: (contentId: string, hotspotId: string, title: string, language: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.CONTENT_CREATED,
      title: `Content "${title}" created (${language})`,
      entityType: "content",
      entityId: contentId,
      userId,
      metadata: { hotspotId, language },
    }),

  updated: (contentId: string, hotspotId: string, title: string, language: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.CONTENT_UPDATED,
      title: `Content "${title}" updated (${language})`,
      entityType: "content",
      entityId: contentId,
      userId,
      metadata: { hotspotId, language },
    }),

  deleted: (contentId: string, hotspotId: string, title: string, language: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.CONTENT_DELETED,
      title: `Content "${title}" deleted (${language})`,
      entityType: "content",
      entityId: contentId,
      userId,
      metadata: { hotspotId, language },
    }),
};

export const logLanguageActivity = {
  created: (languageCode: string, name: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.LANGUAGE_CREATED,
      title: `Language "${name}" added`,
      entityType: "language",
      entityId: languageCode,
      userId,
    }),

  updated: (languageCode: string, name: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.LANGUAGE_UPDATED,
      title: `Language "${name}" updated`,
      entityType: "language",
      entityId: languageCode,
      userId,
    }),

  deleted: (languageCode: string, name: string, userId?: string) =>
    logActivity({
      type: ActivityTypes.LANGUAGE_DELETED,
      title: `Language "${name}" removed`,
      entityType: "language",
      entityId: languageCode,
      userId,
    }),
};

export const logUserActivity = {
  login: (userId: string, email: string) =>
    logActivity({
      type: ActivityTypes.USER_LOGIN,
      title: `User ${email} logged in`,
      entityType: "user",
      entityId: userId,
      userId,
    }),

  logout: (userId: string, email: string) =>
    logActivity({
      type: ActivityTypes.USER_LOGOUT,
      title: `User ${email} logged out`,
      entityType: "user",
      entityId: userId,
      userId,
    }),
};
