import express, { Request, Response, NextFunction } from "express";
import { body, param, validationResult } from "express-validator";
import { prisma } from "../utils/database";
import { authenticate, authorize, AuthRequest } from "../middleware/auth";
import { getCache, setCache, clearCachePattern } from "../utils/redis";
import logger from "../utils/logger";
import {
  logHotspotActivity,
  logContentActivity,
} from "../utils/activityLogger";

const router = express.Router();

// Validation rules
const createHotspotValidation = [
  body("panoramaId").isString(),
  body("xPosition").isFloat({ min: 0, max: 100 }),
  body("yPosition").isFloat({ min: 0, max: 100 }),
  body("iconType").optional().isString(),
  body("iconColor").optional().isString(),
  body("order").optional().isInt({ min: 0 }),
];

const updateHotspotValidation = [
  param("id").isString(),
  body("xPosition").optional().isFloat({ min: 0, max: 100 }),
  body("yPosition").optional().isFloat({ min: 0, max: 100 }),
  body("iconType").optional().isString(),
  body("iconColor").optional().isString(),
  body("order").optional().isInt({ min: 0 }),
  body("isActive").optional().isBoolean(),
];

const createContentValidation = [
  param("hotspotId").isString(),
  body("languageCode").isString(),
  body("title").trim().isLength({ min: 1, max: 255 }),
  body("subtitle").optional().trim().isLength({ min: 1, max: 255 }),
  body("description").optional().trim(),
  // Rich text fields for TipTap editor
  body("descriptionJson").optional().isString(),
  body("descriptionHtml").optional().trim(),
  body("imageUrl").optional().isURL(),
  body("linkUrl").optional().isURL(),
  body("linkText").optional().trim().isLength({ min: 1, max: 100 }),
  // File content fields
  body("contentType").optional().isIn(["standard", "file"]),
  body("fileUrl").optional().isString(),
  body("fileType").optional().isIn(["image", "pdf"]),
  body("fileMetadata").optional().isObject(),
  // Styling fields - optional JSON objects
  body("titleStyle").optional().isObject(),
  body("subtitleStyle").optional().isObject(),
  body("descriptionStyle").optional().isObject(),
  body("linkStyle").optional().isObject(),
  body("imageStyle").optional().isObject(),
];

// GET /api/hotspots - Get all hotspots for admin
router.get(
  "/",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { page = 1, limit = 50, panoramaId } = req.query;
      const skip = (Number(page) - 1) * Number(limit);

      // Build where clause for filtering
      const whereClause: any = {};
      if (panoramaId && panoramaId !== "all") {
        whereClause.panoramaId = panoramaId as string;
      }

      const [hotspots, total] = await Promise.all([
        prisma.hotspot.findMany({
          where: whereClause,
          skip,
          take: Number(limit),
          orderBy: { createdAt: "desc" },
          include: {
            panorama: {
              select: {
                id: true,
                title: true,
              },
            },
            content: {
              include: {
                language: true,
              },
            },
            _count: {
              select: {
                content: true,
              },
            },
          },
        }),
        Object.keys(whereClause).length > 0
          ? prisma.hotspot.count({ where: whereClause })
          : prisma.hotspot.count(),
      ]);

      res.json({
        success: true,
        data: {
          hotspots,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/hotspots/panorama/:panoramaId
router.get(
  "/panorama/:panoramaId",
  [param("panoramaId").isString()],
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { panoramaId } = req.params;
      const { lang = "en" } = req.query;

      // Check cache
      const cacheKey = `hotspots:panorama:${panoramaId}:${lang}`;
      const cached = await getCache(cacheKey);
      if (cached) {
        res.json(cached);
        return;
      }

      // First try to get hotspots with content in the requested language
      let hotspots = await prisma.hotspot.findMany({
        where: {
          panoramaId,
          isActive: true,
        },
        orderBy: { order: "asc" },
        include: {
          content: {
            where: {
              languageCode: lang as string,
            },
            orderBy: {
              order: "asc", // Order products within each language for slideshow
            },
            include: {
              language: true,
            },
          },
        },
      });

      // If no content found in requested language, fallback to any available content
      const hotspotsWithoutContent = hotspots.filter(
        (h) => h.content.length === 0
      );
      if (hotspotsWithoutContent.length > 0) {
        const fallbackHotspots = await prisma.hotspot.findMany({
          where: {
            panoramaId,
            isActive: true,
            id: { in: hotspotsWithoutContent.map((h) => h.id) },
          },
          orderBy: { order: "asc" },
          include: {
            content: {
              orderBy: {
                order: "asc",
              },
              include: {
                language: true,
              },
            },
          },
        });

        // Merge the results - keep hotspots with requested language content,
        // and add fallback content for those without
        hotspots = hotspots.map((hotspot) => {
          if (hotspot.content.length === 0) {
            const fallback = fallbackHotspots.find((f) => f.id === hotspot.id);
            return fallback || hotspot;
          }
          return hotspot;
        });
      }

      const result = {
        success: true,
        data: { hotspots },
      };

      // Cache for 5 minutes
      await setCache(cacheKey, result, 300);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/hotspots/:id
router.get(
  "/:id",
  [param("id").isString()],
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;

      const hotspot = await prisma.hotspot.findUnique({
        where: { id },
        include: {
          content: {
            orderBy: {
              order: "asc", // Order products for slideshow
            },
            include: {
              language: true,
            },
          },
          panorama: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      if (!hotspot) {
        res.status(404).json({
          success: false,
          error: { message: "Hotspot not found" },
        });
        return;
      }

      res.json({
        success: true,
        data: { hotspot },
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/hotspots
router.post(
  "/",
  [authenticate, authorize("admin"), ...createHotspotValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const {
        panoramaId,
        xPosition,
        yPosition,
        iconType = "info",
        iconColor = "#007bff",
        order = 0,
      } = req.body;

      // Verify panorama exists
      const panorama = await prisma.panorama.findUnique({
        where: { id: panoramaId },
      });

      if (!panorama) {
        res.status(404).json({
          success: false,
          error: { message: "Panorama not found" },
        });
        return;
      }

      const hotspot = await prisma.hotspot.create({
        data: {
          panoramaId,
          xPosition,
          yPosition,
          iconType,
          iconColor,
          order,
        },
        include: {
          content: true,
        },
      });

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${panoramaId}:*`);
      await clearCachePattern(`panorama:${panoramaId}`);

      // Log activity
      await logHotspotActivity.created(
        hotspot.id,
        panoramaId,
        panorama.title,
        req.user?.id
      );

      logger.info(`Hotspot created: ${hotspot.id}`);

      res.status(201).json({
        success: true,
        data: { hotspot },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/hotspots/:id
router.put(
  "/:id",
  [authenticate, authorize("admin"), ...updateHotspotValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;
      const updateData = req.body;

      const hotspot = await prisma.hotspot.update({
        where: { id },
        data: updateData,
        include: {
          content: true,
        },
      });

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${hotspot.panoramaId}:*`);
      await clearCachePattern(`panorama:${hotspot.panoramaId}`);

      logger.info(`Hotspot updated: ${hotspot.id}`);

      res.json({
        success: true,
        data: { hotspot },
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/hotspots/:id
router.delete(
  "/:id",
  [authenticate, authorize("admin"), param("id").isString()],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { id } = req.params;

      const hotspot = await prisma.hotspot.findUnique({
        where: { id },
        select: { panoramaId: true },
      });

      if (!hotspot) {
        res.status(404).json({
          success: false,
          error: { message: "Hotspot not found" },
        });
        return;
      }

      await prisma.hotspot.delete({
        where: { id },
      });

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${hotspot.panoramaId}:*`);
      await clearCachePattern(`panorama:${hotspot.panoramaId}`);

      logger.info(`Hotspot deleted: ${id}`);

      res.json({
        success: true,
        data: { message: "Hotspot deleted successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/hotspots/:hotspotId/content
router.post(
  "/:hotspotId/content",
  [authenticate, authorize("admin"), ...createContentValidation],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: { message: "Validation failed", details: errors.array() },
        });
        return;
      }

      const { hotspotId } = req.params;
      const {
        languageCode,
        title,
        subtitle,
        description,
        descriptionJson: descriptionJsonString,
        descriptionHtml,
        imageUrl,
        linkUrl,
        linkText,
        order = 0, // Default order for new products
        contentType = "standard",
        fileUrl,
        fileType,
        fileMetadata,
        titleStyle,
        subtitleStyle,
        descriptionStyle,
        linkStyle,
        imageStyle,
      } = req.body;

      // Parse JSON string to object for database storage
      let descriptionJson = null;
      if (descriptionJsonString) {
        try {
          descriptionJson = JSON.parse(descriptionJsonString);
        } catch (error) {
          console.warn("Failed to parse descriptionJson:", error);
        }
      }

      // Verify hotspot exists
      const hotspot = await prisma.hotspot.findUnique({
        where: { id: hotspotId },
        select: { id: true, panoramaId: true },
      });

      if (!hotspot) {
        res.status(404).json({
          success: false,
          error: { message: "Hotspot not found" },
        });
        return;
      }

      // Verify language exists
      const language = await prisma.language.findUnique({
        where: { code: languageCode },
      });

      if (!language) {
        res.status(404).json({
          success: false,
          error: { message: "Language not found" },
        });
        return;
      }

      const content = await prisma.hotspotContent.create({
        data: {
          hotspotId,
          languageCode,
          title,
          subtitle,
          description,
          descriptionJson,
          descriptionHtml,
          imageUrl,
          linkUrl,
          linkText,
          order, // Include order for slideshow sequencing
          contentType,
          fileUrl,
          fileType,
          fileMetadata,
          titleStyle,
          subtitleStyle,
          descriptionStyle,
          linkStyle,
          imageStyle,
        },
        include: {
          language: true,
        },
      });

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${hotspot.panoramaId}:*`);
      await clearCachePattern(`panorama:${hotspot.panoramaId}`);

      // Log activity
      await logContentActivity.created(
        content.id,
        hotspotId,
        title,
        languageCode,
        req.user?.id
      );

      logger.info(`Hotspot content created: ${content.id}`);

      res.status(201).json({
        success: true,
        data: { content },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/hotspots/:hotspotId/content/reorder - Reorder products in slideshow
router.put(
  "/:hotspotId/content/reorder",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { hotspotId } = req.params;
      const { contentIds } = req.body; // Array of content IDs in new order

      if (!Array.isArray(contentIds)) {
        res.status(400).json({
          success: false,
          error: { message: "contentIds must be an array" },
        });
        return;
      }

      // Verify hotspot exists
      const hotspot = await prisma.hotspot.findUnique({
        where: { id: hotspotId },
        select: { id: true, panoramaId: true },
      });

      if (!hotspot) {
        res.status(404).json({
          success: false,
          error: { message: "Hotspot not found" },
        });
        return;
      }

      // Update order for each content item
      const updatePromises = contentIds.map(
        (contentId: string, index: number) =>
          prisma.hotspotContent.update({
            where: {
              id: contentId,
              hotspotId: hotspotId,
            },
            data: {
              order: index,
            },
          })
      );

      await Promise.all(updatePromises);

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${hotspot.panoramaId}:*`);
      await clearCachePattern(`panorama:${hotspot.panoramaId}`);

      logger.info(`Hotspot content reordered: ${hotspotId}`);

      res.json({
        success: true,
        data: { message: "Content reordered successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/hotspots/:hotspotId/content/:contentId
router.put(
  "/:hotspotId/content/:contentId",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { hotspotId, contentId } = req.params;
      const updateData = req.body;

      const content = await prisma.hotspotContent.update({
        where: {
          id: contentId,
          hotspotId: hotspotId,
        },
        data: updateData,
        include: {
          language: true,
          hotspot: {
            select: { panoramaId: true },
          },
        },
      });

      // Clear cache
      await clearCachePattern(
        `hotspots:panorama:${content.hotspot.panoramaId}:*`
      );
      await clearCachePattern(`panorama:${content.hotspot.panoramaId}`);

      logger.info(`Hotspot content updated: ${content.id}`);

      res.json({
        success: true,
        data: { content },
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/hotspots/:hotspotId/content/:contentId
router.delete(
  "/:hotspotId/content/:contentId",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { hotspotId, contentId } = req.params;

      const content = await prisma.hotspotContent.findUnique({
        where: { id: contentId },
        include: {
          hotspot: {
            select: { panoramaId: true },
          },
        },
      });

      if (!content || content.hotspotId !== hotspotId) {
        res.status(404).json({
          success: false,
          error: { message: "Content not found" },
        });
        return;
      }

      await prisma.hotspotContent.delete({
        where: { id: contentId },
      });

      // Clear cache
      await clearCachePattern(
        `hotspots:panorama:${content.hotspot.panoramaId}:*`
      );
      await clearCachePattern(`panorama:${content.hotspot.panoramaId}`);

      logger.info(`Hotspot content deleted: ${contentId}`);

      res.json({
        success: true,
        data: { message: "Content deleted successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/hotspot-content - Create content (alternative endpoint for frontend)
router.post(
  "/content",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const {
        hotspotId,
        languageCode,
        title,
        subtitle,
        description,
        descriptionJson: descriptionJsonString,
        descriptionHtml,
        imageUrl,
        linkUrl,
        linkText,
        contentType = "standard",
        fileUrl,
        fileType,
        fileMetadata,
        titleStyle,
        subtitleStyle,
        descriptionStyle,
        linkStyle,
        imageStyle,
      } = req.body;

      // Parse JSON string to object for database storage
      let descriptionJson = null;
      if (descriptionJsonString) {
        try {
          descriptionJson = JSON.parse(descriptionJsonString);
        } catch (error) {
          console.warn("Failed to parse descriptionJson:", error);
        }
      }

      // Verify hotspot exists
      const hotspot = await prisma.hotspot.findUnique({
        where: { id: hotspotId },
        select: { id: true, panoramaId: true },
      });

      if (!hotspot) {
        res.status(404).json({
          success: false,
          error: { message: "Hotspot not found" },
        });
        return;
      }

      // Verify language exists
      const language = await prisma.language.findUnique({
        where: { code: languageCode },
      });

      if (!language) {
        res.status(404).json({
          success: false,
          error: { message: "Language not found" },
        });
        return;
      }

      const content = await prisma.hotspotContent.create({
        data: {
          hotspotId,
          languageCode,
          title,
          subtitle,
          description,
          descriptionJson,
          descriptionHtml,
          imageUrl,
          linkUrl,
          linkText,
          contentType,
          fileUrl,
          fileType,
          fileMetadata,
          titleStyle,
          subtitleStyle,
          descriptionStyle,
          linkStyle,
          imageStyle,
        },
        include: {
          language: true,
        },
      });

      // Clear cache
      await clearCachePattern(`hotspots:panorama:${hotspot.panoramaId}:*`);
      await clearCachePattern(`panorama:${hotspot.panoramaId}`);

      logger.info(`Hotspot content created: ${content.id}`);

      res.status(201).json({
        success: true,
        data: { content },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/hotspot-content/:id - Update content (alternative endpoint for frontend)
router.put(
  "/content/:id",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const {
        title,
        subtitle,
        description,
        descriptionJson: descriptionJsonString,
        descriptionHtml,
        imageUrl,
        linkUrl,
        linkText,
        contentType,
        fileUrl,
        fileType,
        fileMetadata,
        titleStyle,
        subtitleStyle,
        descriptionStyle,
        linkStyle,
        imageStyle,
        // Exclude read-only fields
        // languageCode, hotspotId, id, createdAt, updatedAt, language, hotspot
      } = req.body;

      // Parse JSON string to object for database storage
      let descriptionJson = null;
      if (descriptionJsonString) {
        try {
          descriptionJson = JSON.parse(descriptionJsonString);
        } catch (error) {
          console.warn("Failed to parse descriptionJson:", error);
        }
      }

      const updateData = {
        title,
        subtitle,
        description,
        descriptionJson,
        descriptionHtml,
        imageUrl,
        linkUrl,
        linkText,
        contentType,
        fileUrl,
        fileType,
        fileMetadata,
        titleStyle,
        subtitleStyle,
        descriptionStyle,
        linkStyle,
        imageStyle,
      };

      const content = await prisma.hotspotContent.update({
        where: { id },
        data: updateData,
        include: {
          language: true,
          hotspot: {
            select: { panoramaId: true },
          },
        },
      });

      // Clear cache
      await clearCachePattern(
        `hotspots:panorama:${content.hotspot.panoramaId}:*`
      );
      await clearCachePattern(`panorama:${content.hotspot.panoramaId}`);

      logger.info(`Hotspot content updated: ${content.id}`);

      res.json({
        success: true,
        data: { content },
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/hotspot-content/:id - Delete content (alternative endpoint for frontend)
router.delete(
  "/content/:id",
  [authenticate, authorize("admin")],
  async (
    req: AuthRequest,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { id } = req.params;

      const content = await prisma.hotspotContent.findUnique({
        where: { id },
        include: {
          hotspot: {
            select: { panoramaId: true },
          },
        },
      });

      if (!content) {
        res.status(404).json({
          success: false,
          error: { message: "Content not found" },
        });
        return;
      }

      await prisma.hotspotContent.delete({
        where: { id },
      });

      // Clear cache
      await clearCachePattern(
        `hotspots:panorama:${content.hotspot.panoramaId}:*`
      );
      await clearCachePattern(`panorama:${content.hotspot.panoramaId}`);

      logger.info(`Hotspot content deleted: ${id}`);

      res.json({
        success: true,
        data: { message: "Content deleted successfully" },
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
