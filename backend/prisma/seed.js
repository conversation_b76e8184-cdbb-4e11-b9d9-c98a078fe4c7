"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const envFile = process.env.NODE_ENV === "production"
    ? ".env.production"
    : ".env.development";
const envPath = path_1.default.resolve(__dirname, "..", "..", envFile);
try {
    dotenv_1.default.config({ path: envPath });
    console.log(`📄 Loaded environment from: ${envPath}`);
}
catch (error) {
    console.warn("⚠️  Could not load environment file, using process.env");
}
if (!process.env.DATABASE_URL) {
    process.env.DATABASE_URL =
        "postgresql://root:Onamission%23007@37.27.84.251:5432/iduna_db";
    console.log("🔧 Using fallback DATABASE_URL");
}
const prisma = new client_1.PrismaClient();
async function main() {
    console.log("🌱 Starting database seed...");
    const languages = [
        {
            code: "en",
            name: "English",
            nativeName: "English",
            isDefault: true,
            isActive: true,
        },
        {
            code: "da",
            name: "Danish",
            nativeName: "Dansk",
            isDefault: false,
            isActive: true,
        },
        {
            code: "de",
            name: "German",
            nativeName: "Deutsch",
            isDefault: false,
            isActive: true,
        },
        {
            code: "fr",
            name: "French",
            nativeName: "Français",
            isDefault: false,
            isActive: true,
        },
        {
            code: "es",
            name: "Spanish",
            nativeName: "Español",
            isDefault: false,
            isActive: true,
        },
    ];
    console.log("📝 Creating languages...");
    for (const lang of languages) {
        await prisma.language.upsert({
            where: { code: lang.code },
            update: {},
            create: lang,
        });
    }
    const hashedPassword = await bcryptjs_1.default.hash("admin123", 12);
    console.log("👤 Creating admin user...");
    const adminUser = await prisma.user.upsert({
        where: { email: "<EMAIL>" },
        update: {},
        create: {
            email: "<EMAIL>",
            password: hashedPassword,
            firstName: "Admin",
            lastName: "User",
            role: "admin",
            isActive: true,
        },
    });
    console.log("🏠 Creating sample panorama...");
    const samplePanorama = await prisma.panorama.upsert({
        where: { filename: "3d_Kitchen_06.jpg" },
        update: {},
        create: {
            filename: "3d_Kitchen_06.jpg",
            title: "Modern Kitchen 360°",
            description: "A beautiful modern kitchen with interactive hotspots showcasing various features and appliances.",
            width: 11520,
            height: 6480,
            fileSize: 26900000,
            mimeType: "image/jpeg",
            isActive: true,
        },
    });
    console.log("📍 Creating sample hotspots...");
    const hotspot1 = await prisma.hotspot.create({
        data: {
            panoramaId: samplePanorama.id,
            xPosition: 25.5,
            yPosition: 45.2,
            iconType: "info",
            iconColor: "#007bff",
            order: 1,
            isActive: true,
        },
    });
    const hotspot2 = await prisma.hotspot.create({
        data: {
            panoramaId: samplePanorama.id,
            xPosition: 65.8,
            yPosition: 38.7,
            iconType: "info",
            iconColor: "#28a745",
            order: 2,
            isActive: true,
        },
    });
    const hotspot3 = await prisma.hotspot.create({
        data: {
            panoramaId: samplePanorama.id,
            xPosition: 80.3,
            yPosition: 52.1,
            iconType: "info",
            iconColor: "#dc3545",
            order: 3,
            isActive: true,
        },
    });
    console.log("📄 Creating sample content...");
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot1.id,
            languageCode: "en",
            title: "Iduna Kitchen Surface Cleaner",
            subtitle: "Premium Multi-Surface Formula",
            description: "Iduna's advanced kitchen surface cleaner removes grease, grime, and bacteria from all kitchen surfaces. Our eco-friendly formula is safe for food preparation areas while providing powerful cleaning action. Perfect for countertops, appliances, and cabinet surfaces.",
            imageUrl: "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/kitchen-surface-cleaner",
            linkText: "Shop at Iduna.dk",
        },
    });
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot1.id,
            languageCode: "da",
            title: "Iduna Køkken Overfladerenser",
            subtitle: "Premium Multi-Overflade Formel",
            description: "Idunas avancerede køkkenoverfladerenser fjerner fedt, snavs og bakterier fra alle køkkenoverflader. Vores miljøvenlige formel er sikker til madlavningsområder, mens den giver kraftig rengøringsvirkning. Perfekt til bordplader, apparater og skabsoverflader.",
            imageUrl: "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/koekken-overfladerenser",
            linkText: "Shop på Iduna.dk",
        },
    });
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot2.id,
            languageCode: "en",
            title: "Iduna Degreaser Pro",
            subtitle: "Heavy-Duty Kitchen Degreaser",
            description: "Tackle the toughest kitchen grease with Iduna's professional-grade degreaser. Specially formulated for range hoods, ovens, and cooking surfaces. Cuts through baked-on grease and food residue while being gentle on your appliances.",
            imageUrl: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/degreaser-pro",
            linkText: "View Product Details",
        },
    });
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot2.id,
            languageCode: "da",
            title: "Iduna Fedtfjerner Pro",
            subtitle: "Heavy-Duty Køkken Fedtfjerner",
            description: "Bekæmp det hårdeste køkkenfedt med Idunas professionelle fedtfjerner. Specielt formuleret til emhætter, ovne og kogeplader. Skærer gennem bagpå fedt og madrester, mens den er skånsom mod dine apparater.",
            imageUrl: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/fedtfjerner-pro",
            linkText: "Se Produktdetaljer",
        },
    });
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot3.id,
            languageCode: "en",
            title: "Iduna Dishwasher Tablets",
            subtitle: "Eco-Friendly Cleaning Power",
            description: "Premium dishwasher tablets that deliver sparkling clean dishes while protecting your dishwasher. Our phosphate-free formula is tough on stains but gentle on the environment. Each tablet provides complete cleaning, rinsing, and protection.",
            imageUrl: "https://images.unsplash.com/photo-1563453392212-326f5e854473?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/dishwasher-tablets",
            linkText: "Order Now",
        },
    });
    await prisma.hotspotContent.create({
        data: {
            hotspotId: hotspot3.id,
            languageCode: "da",
            title: "Iduna Opvaskemaskine Tabletter",
            subtitle: "Miljøvenlig Rengøringskraft",
            description: "Premium opvasketabletter, der leverer gnidende rent service, mens de beskytter din opvaskemaskine. Vores fosfatfri formel er hård mod pletter, men skånsom mod miljøet. Hver tablet giver komplet rengøring, skylning og beskyttelse.",
            imageUrl: "https://images.unsplash.com/photo-1563453392212-326f5e854473?w=500&h=300&fit=crop",
            linkUrl: "https://iduna.dk/opvaskemaskine-tabletter",
            linkText: "Bestil Nu",
        },
    });
    console.log("✅ Database seed completed successfully!");
    console.log(`👤 Admin user created: <EMAIL> / admin123`);
    console.log(`🏠 Sample panorama created: ${samplePanorama.title}`);
    console.log(`📍 Created ${3} sample hotspots with multilingual content`);
}
main()
    .catch((e) => {
    console.error("❌ Seed failed:", e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map